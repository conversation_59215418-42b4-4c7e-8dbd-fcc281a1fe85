// Crop tier levels
export type CropTier = 'Common' | 'Uncommon' | 'Rare' | 'Legendary' | 'Mythical' | 'Divine' | 'Prismatic' | 'Transcendent';

// 收获类型
export type HarvestType = 'Single' | 'Multi';

// 变异类型分类
export type MutationType = 'Growth' | 'Temperature' | 'Environmental';

// 作物数据结构
export interface Crop {
  id: string;
  name: string;
  image: string;
  shecklePrice: number;
  robuxPrice: number;
  minimumValue: number;
  minimumWeight: number;
  tier: CropTier;
  harvestType: HarvestType;
  obtainable: boolean;
  description?: string;
  k?: number; // 用于重量计算的常数
}

// 变异数据结构
export interface Mutation {
  id: string;
  name: string;
  image: string;
  multiplier: number;
  stackBonus: number;
  type: MutationType;
  description: string;
  triggers: string;
  constraints?: string[];
  incompatibleWith?: string[];
}

// 计算器输入数据
export interface CalculatorInput {
  crop: Crop | null;
  weight: number;
  quantity: number;
  friendBoost: number;
  selectedMutations: Mutation[];
}

// 计算结果
export interface CalculationResult {
  totalValue: number;
  totalMultiplier: number;
  baseValue: number;
  mutationMultiplier: number;
  details: {
    cropName: string;
    weight: number;
    quantity: number;
    friendBoost: number;
    mutations: string[];
  };
}

// 变异约束规则
export interface MutationConstraint {
  group: string;
  mutationIds: string[];
  maxAllowed: number;
  description: string;
}
