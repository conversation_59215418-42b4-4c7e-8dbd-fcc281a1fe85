'use client';

import React from 'react';
import Image from 'next/image';
import { Crop } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatSheckles } from '@/lib/calculator';

interface CropCardProps {
  crop: Crop;
  onClick?: () => void;
}

export function CropCard({ crop, onClick }: CropCardProps) {
  const tierColors = {
    'Common': 'bg-gray-100 text-gray-800 border-gray-300',
    'Uncommon': 'bg-green-100 text-green-800 border-green-300',
    'Rare': 'bg-blue-100 text-blue-800 border-blue-300',
    'Legendary': 'bg-purple-100 text-purple-800 border-purple-300',
    'Mythical': 'bg-pink-100 text-pink-800 border-pink-300',
    'Divine': 'bg-yellow-100 text-yellow-800 border-yellow-300',
    'Prismatic': 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-300',
    'Transcendent': 'bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-800 border-orange-300'
  };

  return (
    <Card
      className={`transition-all hover:shadow-lg border-t-4 ${tierColors[crop.tier].split(' ')[0]} ${onClick ? 'cursor-pointer hover:scale-105' : ''}`}
      onClick={onClick}
    >
      <CardHeader className="pb-2 pt-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-bold">{crop.name}</CardTitle>
          <span className={`px-3 py-1 text-xs rounded-full border font-medium ${tierColors[crop.tier]}`}>
            {crop.tier}
          </span>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Crop Image and Tags */}
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0">
            <Image
              src={crop.image}
              alt={crop.name}
              width={80}
              height={80}
              className="rounded-lg border border-border/30 p-1 bg-background/50"
            />
          </div>
          <div className="flex flex-col gap-2">
            <span className={`px-3 py-1 rounded-full text-xs font-medium w-fit ${
              crop.harvestType === 'Multi'
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-orange-100 text-orange-800 border border-orange-200'
            }`}>
              {crop.harvestType === 'Multi' ? 'Multi Harvest' : 'Single Harvest'}
            </span>
            <span className={`px-3 py-1 rounded-full text-xs font-medium w-fit ${
              crop.obtainable
                ? 'bg-blue-100 text-blue-800 border border-blue-200'
                : 'bg-red-100 text-red-800 border border-red-200'
            }`}>
              {crop.obtainable ? 'Obtainable' : 'Unobtainable'}
            </span>
          </div>
        </div>

        {/* Basic Information */}
        <div className="bg-background/50 rounded-lg border border-border/30 p-3">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3 text-sm">
            <div>
              <span className="text-muted-foreground text-xs">Seed Price:</span>
              <div className="font-semibold">{formatSheckles(crop.shecklePrice)}</div>
            </div>
            <div>
              <span className="text-muted-foreground text-xs">Robux Price:</span>
              <div className="font-semibold">R$ {crop.robuxPrice}</div>
            </div>
            <div>
              <span className="text-muted-foreground text-xs">Min Value:</span>
              <div className="font-semibold">{formatSheckles(crop.minimumValue)}</div>
            </div>
            <div>
              <span className="text-muted-foreground text-xs">Min Weight:</span>
              <div className="font-semibold">{crop.minimumWeight}kg</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
