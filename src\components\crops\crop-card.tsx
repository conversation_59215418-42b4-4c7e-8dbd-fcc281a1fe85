'use client';

import React from 'react';
import Image from 'next/image';
import { Crop } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatSheckles } from '@/lib/calculator';

interface CropCardProps {
  crop: Crop;
  onClick?: () => void;
}

export function CropCard({ crop, onClick }: CropCardProps) {
  const tierColors = {
    'Common': 'bg-gray-100 text-gray-800 border-gray-300',
    'Uncommon': 'bg-green-100 text-green-800 border-green-300',
    'Rare': 'bg-blue-100 text-blue-800 border-blue-300',
    'Legendary': 'bg-purple-100 text-purple-800 border-purple-300',
    'Mythical': 'bg-pink-100 text-pink-800 border-pink-300',
    'Divine': 'bg-yellow-100 text-yellow-800 border-yellow-300',
    'Prismatic': 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-300',
    'Transcendent': 'bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-800 border-orange-300'
  };

  return (
    <Card 
      className={`transition-all hover:shadow-lg ${onClick ? 'cursor-pointer hover:scale-105' : ''}`}
      onClick={onClick}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{crop.name}</CardTitle>
          <span className={`px-2 py-1 text-xs rounded-full border ${tierColors[crop.tier]}`}>
            {crop.tier}
          </span>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Crop Image */}
        <div className="flex justify-center">
          <Image
            src={crop.image}
            alt={crop.name}
            width={80}
            height={80}
            className="rounded-lg"
          />
        </div>

        {/* Basic Information */}
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-muted-foreground">Seed Price:</span>
              <div className="font-semibold">{formatSheckles(crop.shecklePrice)}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Robux Price:</span>
              <div className="font-semibold">R$ {crop.robuxPrice}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Min Value:</span>
              <div className="font-semibold">{formatSheckles(crop.minimumValue)}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Min Weight:</span>
              <div className="font-semibold">{crop.minimumWeight}kg</div>
            </div>
          </div>

          <div className="flex justify-between items-center text-sm">
            <span className={`px-2 py-1 rounded text-xs ${
              crop.harvestType === 'Multi'
                ? 'bg-green-100 text-green-800'
                : 'bg-orange-100 text-orange-800'
            }`}>
              {crop.harvestType === 'Multi' ? 'Multi Harvest' : 'Single Harvest'}
            </span>
            <span className={`px-2 py-1 rounded text-xs ${
              crop.obtainable
                ? 'bg-blue-100 text-blue-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {crop.obtainable ? 'Obtainable' : 'Unobtainable'}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
