import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Grow A Garden Crops Database - Complete List of All Crops | Grow A Garden Calculator',
  description: 'Complete database of all 100+ crops in Roblox Grow A Garden. View crop tiers, values, weights, and harvest types. Filter by rarity and search crops.',
  keywords: 'Grow A Garden crops, Roblox crops, crop database, crop tiers, crop values, farming guide, Grow A Garden Calculator',
  openGraph: {
    title: 'Grow A Garden Crops Database - Complete List of All Crops',
    description: 'Complete database of all 100+ crops in Roblox Grow A Garden. View crop tiers, values, weights, and harvest types.',
    url: 'https://growagardencalculator.com/crops',
  },
};

export default function CropsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
