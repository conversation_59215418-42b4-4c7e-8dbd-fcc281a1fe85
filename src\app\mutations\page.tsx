'use client';

import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Grow A Garden Mutations Guide - All Mutation Types & Multipliers | Grow A Garden Calculator',
  description: 'Complete guide to all 40+ mutations in Roblox Grow A Garden. Learn about Growth, Temperature, and Environmental mutations with multipliers and triggers.',
  keywords: 'Grow A Garden mutations, Roblox mutations, mutation guide, mutation multipliers, Golden mutation, Rainbow mutation, Grow A Garden Calculator',
  openGraph: {
    title: 'Grow A Garden Mutations Guide - All Mutation Types & Multipliers',
    description: 'Complete guide to all 40+ mutations in Roblox Grow A Garden. Learn about Growth, Temperature, and Environmental mutations.',
    url: 'https://growagardencalculator.com/mutations',
  },
};

import React from 'react';
import { Mutation } from '@/types';
import { allMutations } from '@/data/mutations';
import { MutationCard } from '@/components/mutations/mutation-card';
import { MutationFilters } from '@/components/mutations/mutation-filters';
import { MutationStats } from '@/components/mutations/mutation-stats';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function MutationsPage() {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedType, setSelectedType] = React.useState('all');
  const [minMultiplier, setMinMultiplier] = React.useState(0);
  const [maxMultiplier, setMaxMultiplier] = React.useState(999);
  const [sortBy, setSortBy] = React.useState<'name' | 'multiplier' | 'type'>('type');
  const [sortOrder, setSortOrder] = React.useState<'asc' | 'desc'>('asc');

  // Filter mutations
  const filteredMutations = React.useMemo(() => {
    let filtered = allMutations;

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(mutation =>
        mutation.name.toLowerCase().includes(query) ||
        mutation.description.toLowerCase().includes(query)
      );
    }

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(mutation => mutation.type === selectedType);
    }

    // Filter by multiplier range
    filtered = filtered.filter(mutation =>
      mutation.multiplier >= minMultiplier && mutation.multiplier <= maxMultiplier
    );

    // Sort
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'multiplier':
          aValue = a.multiplier;
          bValue = b.multiplier;
          break;
        case 'type':
          const typeOrder = ['Growth', 'Temperature', 'Environmental'];
          aValue = typeOrder.indexOf(a.type);
          bValue = typeOrder.indexOf(b.type);
          break;
        default:
          return 0;
      }

      if (typeof aValue === 'string') {
        return sortOrder === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        return sortOrder === 'asc'
          ? aValue - bValue
          : bValue - aValue;
      }
    });

    return filtered;
  }, [searchQuery, selectedType, minMultiplier, maxMultiplier, sortBy, sortOrder]);

  // Reset filters
  const resetFilters = () => {
    setSearchQuery('');
    setSelectedType('all');
    setMinMultiplier(0);
    setMaxMultiplier(999);
    setSortBy('type');
    setSortOrder('asc');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Title */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Mutation System</h1>
        <p className="text-muted-foreground">Learn about all mutation types in Grow A Garden</p>
      </div>

      {/* Mutation Formula Explanation */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Mutation Formula</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="text-sm space-y-1">
            <p><strong>Total Multiplier = Growth Mutation × (1 + Sum of Environmental Stack Bonuses)</strong></p>
            <p className="text-muted-foreground">• Growth Mutations: Only one can be selected (Golden or Rainbow)</p>
            <p className="text-muted-foreground">• Temperature Mutations: Only one can be selected</p>
            <p className="text-muted-foreground">• Environmental Mutations: Can stack with specific compatibility rules</p>
            <p className="text-muted-foreground">• Stack Bonus = Mutation Multiplier - 1</p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Side: Filters and Stats */}
        <div className="lg:col-span-1 space-y-6">
          <MutationFilters
            searchQuery={searchQuery}
            selectedType={selectedType}
            minMultiplier={minMultiplier}
            maxMultiplier={maxMultiplier}
            onSearchChange={setSearchQuery}
            onTypeChange={setSelectedType}
            onMinMultiplierChange={setMinMultiplier}
            onMaxMultiplierChange={setMaxMultiplier}
            onReset={resetFilters}
          />

          <MutationStats mutations={allMutations} filteredMutations={filteredMutations} />
        </div>

        {/* Right Side: Mutation List */}
        <div className="lg:col-span-3">
          {/* Sort Options */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="type">Type</option>
                <option value="name">Name</option>
                <option value="multiplier">Multiplier</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="text-sm px-2 py-1 border rounded hover:bg-muted"
              >
                {sortOrder === 'asc' ? 'Ascending' : 'Descending'}
              </button>
            </div>
            <div className="text-sm text-muted-foreground">
              Showing {filteredMutations.length} / {allMutations.length} mutations
            </div>
          </div>

          {/* Mutation Grid */}
          {filteredMutations.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredMutations.map((mutation) => (
                <MutationCard key={mutation.id} mutation={mutation} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-muted-foreground mb-4">
                No matching mutations found
              </div>
              <Button onClick={resetFilters} variant="outline">
                Reset Filters
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
