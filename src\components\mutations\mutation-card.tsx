'use client';

import React from 'react';
import Image from 'next/image';
import { Mutation } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface MutationCardProps {
  mutation: Mutation;
}

export function MutationCard({ mutation }: MutationCardProps) {
  const typeColors = {
    'Growth': 'bg-yellow-100 text-yellow-800 border-yellow-300',
    'Temperature': 'bg-blue-100 text-blue-800 border-blue-300',
    'Environmental': 'bg-green-100 text-green-800 border-green-300'
  };

  const getMultiplierColor = (multiplier: number) => {
    if (multiplier >= 100) return 'text-red-600 font-bold';
    if (multiplier >= 50) return 'text-orange-600 font-bold';
    if (multiplier >= 20) return 'text-purple-600 font-bold';
    if (multiplier >= 10) return 'text-blue-600 font-semibold';
    if (multiplier >= 5) return 'text-green-600 font-semibold';
    return 'text-gray-600 font-medium';
  };

  return (
    <Card className="transition-all hover:shadow-lg">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Image
              src={mutation.image}
              alt={mutation.name}
              width={32}
              height={32}
              className="rounded"
            />
            {mutation.name}
          </CardTitle>
          <span className={`px-2 py-1 text-xs rounded-full border ${typeColors[mutation.type]}`}>
            {mutation.type}
          </span>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Multiplier Display */}
        <div className="text-center">
          <div className={`text-3xl ${getMultiplierColor(mutation.multiplier)}`}>
            ×{mutation.multiplier}
          </div>
          <div className="text-sm text-muted-foreground">
            Multiplier {mutation.stackBonus > 0 && `• Stack Bonus: +${mutation.stackBonus}`}
          </div>
        </div>

        {/* Description */}
        <div className="space-y-2">
          <h4 className="font-semibold text-sm">Description</h4>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {mutation.description}
          </p>
        </div>

        {/* Triggers */}
        <div className="space-y-2">
          <h4 className="font-semibold text-sm">Triggers</h4>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {mutation.triggers}
          </p>
        </div>

        {/* Constraints */}
        {mutation.constraints && mutation.constraints.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-semibold text-sm">Constraints</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {mutation.constraints.map((constraint, index) => (
                <li key={index} className="flex items-start gap-1">
                  <span className="text-orange-500 mt-0.5">•</span>
                  {constraint}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Incompatible Mutations */}
        {mutation.incompatibleWith && mutation.incompatibleWith.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-semibold text-sm">Incompatible With</h4>
            <div className="flex flex-wrap gap-1">
              {mutation.incompatibleWith.map((incompatible, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded"
                >
                  {incompatible}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Rarity Indicator */}
        <div className="pt-2 border-t">
          <div className="flex justify-between items-center text-xs">
            <span className="text-muted-foreground">Rarity</span>
            <div className="flex">
              {Array.from({ length: 5 }, (_, i) => (
                <span
                  key={i}
                  className={`w-2 h-2 rounded-full mr-1 ${
                    i < Math.min(5, Math.ceil(mutation.multiplier / 20))
                      ? 'bg-yellow-400'
                      : 'bg-gray-200'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
