'use client';

import React from 'react';
import Image from 'next/image';
import { Crop } from '@/types';
import { crops, cropsByTier } from '@/data/crops';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface CropSelectorProps {
  selectedCrop: Crop | null;
  onCropSelect: (crop: Crop) => void;
}

export function CropSelector({ selectedCrop, onCropSelect }: CropSelectorProps) {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedTier, setSelectedTier] = React.useState<string>('all');

  // Filter crops
  const filteredCrops = React.useMemo(() => {
    let filtered = crops;

    // Filter by tier
    if (selectedTier !== 'all') {
      filtered = filtered.filter(crop => crop.tier === selectedTier);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(crop =>
        crop.name.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [searchQuery, selectedTier]);

  const tiers = ['Common', 'Uncommon', 'Rare', 'Legendary', 'Mythical', 'Divine', 'Prismatic', 'Transcendent'];

  return (
    <Card className="bg-card/50 border-border/50">
      <CardContent className="p-4 space-y-4">
        {/* Search Input */}
        <div className="relative">
          <Input
            placeholder="Select Crop - enter text to search for crops"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-background/50 border-border/50 text-sm"
          />
        </div>

        {/* Crop Grid */}
        <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-2 max-h-80 overflow-y-auto bg-background/30 p-3 rounded-lg border border-border/30">
          {filteredCrops.map((crop) => (
            <button
              key={crop.id}
              onClick={() => onCropSelect(crop)}
              className={`
                relative p-2 rounded-lg transition-all hover:scale-105 group
                ${selectedCrop?.id === crop.id
                  ? 'bg-primary/20 border-2 border-primary'
                  : 'bg-background/50 border border-border/50 hover:border-primary/50 hover:bg-background/80'
                }
              `}
              title={`${crop.name} (${crop.tier})`}
            >
              <Image
                src={crop.image}
                alt={crop.name}
                width={32}
                height={32}
                className="w-8 h-8 mx-auto rounded"
              />
              <p className="text-xs mt-1 truncate text-center">{crop.name}</p>
              {selectedCrop?.id === crop.id && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full"></div>
              )}
            </button>
          ))}
        </div>

        {filteredCrops.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No matching crops found
          </div>
        )}
      </CardContent>
    </Card>
  );
}
