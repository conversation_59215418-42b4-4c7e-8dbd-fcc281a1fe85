'use client';

import React from 'react';
import Image from 'next/image';
import { Crop } from '@/types';
import { crops, cropsByTier } from '@/data/crops';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface CropSelectorProps {
  selectedCrop: Crop | null;
  onCropSelect: (crop: Crop) => void;
}

export function CropSelector({ selectedCrop, onCropSelect }: CropSelectorProps) {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedTier, setSelectedTier] = React.useState<string>('all');

  // Filter crops
  const filteredCrops = React.useMemo(() => {
    let filtered = crops;

    // Filter by tier
    if (selectedTier !== 'all') {
      filtered = filtered.filter(crop => crop.tier === selectedTier);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(crop =>
        crop.name.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [searchQuery, selectedTier]);

  const tiers = ['Common', 'Uncommon', 'Rare', 'Legendary', 'Mythical', 'Divine', 'Prismatic', 'Transcendent'];

  return (
    <Card className="bg-card/50 border-border/50">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Select Crop</CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0 space-y-4">
        {/* Search Input */}
        <div className="relative">
          <Input
            placeholder="Search crops by name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="bg-background/50 border-border/50 text-sm h-10"
          />
        </div>

        {/* Tier Filter */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-muted-foreground">Filter by tier:</span>
          <Select value={selectedTier} onValueChange={setSelectedTier}>
            <SelectTrigger className="w-40 h-8 text-sm">
              <SelectValue placeholder="All tiers" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Tiers</SelectItem>
              {tiers.map((tier) => (
                <SelectItem key={tier} value={tier}>{tier}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Crop Grid */}
        <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 xl:grid-cols-14 gap-3 max-h-96 overflow-y-auto bg-background/30 p-4 rounded-lg border border-border/30">
          {filteredCrops.map((crop) => (
            <button
              key={crop.id}
              onClick={() => onCropSelect(crop)}
              className={`
                relative p-3 rounded-lg transition-all hover:scale-105 group flex flex-col items-center min-h-[80px]
                ${selectedCrop?.id === crop.id
                  ? 'bg-primary/20 border-2 border-primary shadow-md'
                  : 'bg-background/50 border border-border/50 hover:border-primary/50 hover:bg-background/80 hover:shadow-sm'
                }
              `}
              title={`${crop.name} (${crop.tier})`}
              type="button"
            >
              <Image
                src={crop.image}
                alt={crop.name}
                width={32}
                height={32}
                className="w-8 h-8 rounded mb-1"
              />
              <p className="text-xs text-center leading-tight line-clamp-2 max-w-full">{crop.name}</p>
              {selectedCrop?.id === crop.id && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full border border-background"></div>
              )}
            </button>
          ))}
        </div>

        {filteredCrops.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p className="text-sm">No matching crops found</p>
            <p className="text-xs mt-1">Try adjusting your search or filter</p>
          </div>
        )}

        {/* Selected Crop Info */}
        {selectedCrop && (
          <div className="bg-primary/5 border border-primary/20 rounded-lg p-3">
            <div className="flex items-center gap-3">
              <Image
                src={selectedCrop.image}
                alt={selectedCrop.name}
                width={40}
                height={40}
                className="w-10 h-10 rounded"
              />
              <div className="flex-1">
                <h4 className="font-medium text-sm">{selectedCrop.name}</h4>
                <p className="text-xs text-muted-foreground">{selectedCrop.tier} • Min Weight: {selectedCrop.minimumWeight}kg</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
