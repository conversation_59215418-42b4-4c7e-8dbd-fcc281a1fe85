import { Mutation, MutationConstraint } from '@/types';

// Growth Mutations
export const growthMutations: Mutation[] = [
  {
    id: 'golden',
    name: '<PERSON>',
    image: 'https://img.growagardencalculator.net/mutations/Golden.webp',
    multiplier: 20,
    stackBonus: 0,
    type: 'Growth',
    description: 'Has a 1% chance to replace the standard crop variant or can be applied by Dragonfly. Plants display a shining, golden appearance.',
    triggers: '1% chance to replace standard crop variant or can be applied by Dragonfly'
  },
  {
    id: 'rainbow',
    name: '<PERSON>',
    image: 'https://img.growagardencalculator.net/mutations/Rainbow.webp',
    multiplier: 50,
    stackBonus: 0,
    type: 'Growth',
    description: 'Has a 0.1% chance to replace the standard crop variant or can be applied by <PERSON>. Plants continuously cycle through colors, emitting yellow particles and displaying a rainbow above.',
    triggers: '0.1% chance to replace standard crop variant or can be applied by <PERSON>'
  },

];

// Temperature Mutations
export const temperatureMutations: Mutation[] = [
  {
    id: 'wet',
    name: 'Wet',
    image: 'https://img.growagardencalculator.net/mutations/Wet.webp',
    multiplier: 2,
    stackBonus: 1,
    type: 'Temperature',
    description: 'Triggered during Rain or Thunderstorm events, or with a low probability from Sprinklers. Affected plants exhibit water droplets on their surface.',
    triggers: 'Triggered during Rain or Thunderstorm events, or with a low probability from Sprinklers'
  },
  {
    id: 'chilled',
    name: 'Chilled',
    image: 'https://img.growagardencalculator.net/mutations/Chilled.webp',
    multiplier: 2,
    stackBonus: 1,
    type: 'Temperature',
    description: 'Occurs during Frost events or through interaction with a Polar Bear. Plants display a bluish tint and frost particles.',
    triggers: 'Occurs during Frost events or through interaction with a Polar Bear'
  },
  {
    id: 'drenched',
    name: 'Drenched',
    image: 'https://img.growagardencalculator.net/mutations/Drenched.webp',
    multiplier: 5,
    stackBonus: 4,
    type: 'Temperature',
    description: 'This mutation is applied to crops during a Tropical Rain event, where it supersedes the Wet mutation. Visually, it is characterized by large water droplets descending from the crop, which also exhibits a slightly saturated appearance.',
    triggers: 'Applied to crops during a Tropical Rain event'
  },
  {
    id: 'frozen',
    name: 'Frozen',
    image: 'https://img.growagardencalculator.net/mutations/Frozen.webp',
    multiplier: 10,
    stackBonus: 9,
    type: 'Temperature',
    description: 'Occurs during Frost when crops are both Wet and Chilled, or through Polar Bear interaction. Plants are encased in an ice block.',
    triggers: 'Occurs during Frost when crops are both Wet and Chilled, or through Polar Bear interaction'
  },

];

// Environmental Mutations
export const environmentalMutations: Mutation[] = [
  {
    id: 'choc',
    name: 'Choc',
    image: 'https://img.growagardencalculator.net/mutations/Choc.webp',
    multiplier: 2,
    stackBonus: 1,
    type: 'Environmental',
    description: 'Applied by placing a Chocolate Sprinkler, available during the Easter Event 2025 or admin-triggered Chocolate Rain events.',
    triggers: 'Applied by placing a Chocolate Sprinkler, available during Easter Event 2025'
  },
  {
    id: 'moonlit',
    name: 'Moonlit',
    image: 'https://img.growagardencalculator.net/mutations/Moonlit.webp',
    multiplier: 2,
    stackBonus: 1,
    type: 'Environmental',
    description: 'Activates at night, affecting six plants every two minutes within a 10-minute period. Plants emit a purple glow with a shining appearance.',
    triggers: 'Activates at night, affecting six plants every two minutes within a 10-minute period'
  },
  {
    id: 'windstruck',
    name: 'Windstruck',
    image: 'https://img.growagardencalculator.net/mutations/Windstruck.webp',
    multiplier: 2,
    stackBonus: 1,
    type: 'Environmental',
    description: 'Triggered during Windy or Gale conditions. With wind gusts swooping around the crop for a dynamic visual effect.',
    triggers: 'Triggered during Windy or Gale conditions'
  },
  {
    id: 'pollinated',
    name: 'Pollinated',
    image: 'https://img.growagardencalculator.net/mutations/Pollinated.webp',
    multiplier: 3,
    stackBonus: 2,
    type: 'Environmental',
    description: 'Triggered during Bee Swarm events or by specific bee pets (Bee, Honey Bee, Petal Bee, Queen Bee). Plants exhibit a yellow shine with yellow gas-like particles.',
    triggers: 'Triggered during Bee Swarm events or by specific bee pets'
  },
  {
    id: 'sandy',
    name: 'Sandy',
    image: 'https://img.growagardencalculator.net/mutations/Sandy.webp',
    multiplier: 3,
    stackBonus: 2,
    type: 'Environmental',
    description: 'Triggered during a Sandstorm event. The crop appears tan in color and emits puffs of sand around the fruit.',
    triggers: 'Triggered during a Sandstorm event'
  },
  {
    id: 'bloodlit',
    name: 'Bloodlit',
    image: 'https://img.growagardencalculator.net/mutations/Bloodlit.webp',
    multiplier: 4,
    stackBonus: 3,
    type: 'Environmental',
    description: 'Occurs during the Blood Moon Event. Affected plants display a red, radiant appearance.',
    triggers: 'Occurs during the Blood Moon Event'
  },
  {
    id: 'burnt',
    name: 'Burnt',
    image: 'https://img.growagardencalculator.net/mutations/Burnt.webp',
    multiplier: 4,
    stackBonus: 3,
    type: 'Environmental',
    description: 'Crops may be affected by the \'Burnt\' mutation triggered by the Cooked Owl. Plants exhibiting this mutation will display a blackened model, accompanied by a sparking effect at the top when unharvested.',
    triggers: 'Triggered by the Cooked Owl'
  },
  {
    id: 'verdant',
    name: 'Verdant',
    image: 'https://img.growagardencalculator.net/mutations/Verdant.webp',
    multiplier: 4,
    stackBonus: 3,
    type: 'Environmental',
    description: 'May occasionally be triggered by the Scarlet Macaw pet. Showcasing a green hue and releasing green rectangular particles.',
    triggers: 'May occasionally be triggered by the Scarlet Macaw pet'
  },
  {
    id: 'wiltproof',
    name: 'Wiltproof',
    image: 'https://img.growagardencalculator.net/mutations/Wiltproof.webp',
    multiplier: 4,
    stackBonus: 3,
    type: 'Environmental',
    description: 'This mutation is obtainable during the Drought weather event. Its visual characteristics are currently unknown.',
    triggers: 'Obtainable during the Drought weather event'
  },
  {
    id: 'plasma',
    name: 'Plasma',
    image: 'https://img.growagardencalculator.net/mutations/Plasma.webp',
    multiplier: 5,
    stackBonus: 4,
    type: 'Environmental',
    description: 'Triggered during admin-spawned Laser Storm events. Plants exhibit a static pinkish-purple glow with intermittent red flashes.',
    triggers: 'Triggered during admin-spawned Laser Storm events'
  },
  {
    id: 'honeyglazed',
    name: 'HoneyGlazed',
    image: 'https://img.growagardencalculator.net/mutations/HoneyGlazed.webp',
    multiplier: 5,
    stackBonus: 4,
    type: 'Environmental',
    description: 'Applied by placing a Honey Sprinkler or through interaction with Bear Bee. Plants are surrounded by yellow fog and drip with yellow liquid.',
    triggers: 'Applied by placing a Honey Sprinkler or through interaction with Bear Bee'
  },
  {
    id: 'heavenly',
    name: 'Heavenly',
    image: 'https://img.growagardencalculator.net/mutations/Heavenly.webp',
    multiplier: 5,
    stackBonus: 4,
    type: 'Environmental',
    description: 'Triggered during admin-spawned Floating Jandel events. Plants emit golden, radiant light from their base.',
    triggers: 'Triggered during admin-spawned Floating Jandel events'
  },
  {
    id: 'twisted',
    name: 'Twisted',
    image: 'https://img.growagardencalculator.net/mutations/Twisted.webp',
    multiplier: 5,
    stackBonus: 4,
    type: 'Environmental',
    description: 'Activated during Tornado conditions, featuring tornado-like swirls emanating from the crop.',
    triggers: 'Activated during Tornado conditions'
  },
  {
    id: 'cloudtouched',
    name: 'Cloudtouched',
    image: 'https://img.growagardencalculator.net/mutations/Cloudtouched.webp',
    multiplier: 5,
    stackBonus: 4,
    type: 'Environmental',
    description: 'This mutation can be induced using the Mutation Spray Cloudtouched or has a low probability of being applied by the Hyacinth Macaw pet. The affected crop is enveloped in a distinct cloud-like aura.',
    triggers: 'Can be induced using Mutation Spray Cloudtouched or by Hyacinth Macaw pet'
  },
  {
    id: 'clay',
    name: 'Clay',
    image: 'https://img.growagardencalculator.net/mutations/Clay.webp',
    multiplier: 5,
    stackBonus: 4,
    type: 'Environmental',
    description: 'Occurs by combining the \'Wet\' and \'Sandy\' mutations. The crop appears a brownish color and unique texture.',
    triggers: 'Occurs by combining the Wet and Sandy mutations'
  },
  {
    id: 'shocked',
    name: 'Shocked',
    image: 'https://img.growagardencalculator.net/mutations/Shocked.webp',
    multiplier: 100,
    stackBonus: 99,
    type: 'Environmental',
    description: 'Triggered by lightning strikes during Thunderstorm events or Jandel Storm. Plants exhibit a bright, neon glow, lacking the typical studded texture.',
    triggers: 'Triggered by lightning strikes during Thunderstorm events or Jandel Storm'
  },
  {
    id: 'celestial',
    name: 'Celestial',
    image: 'https://img.growagardencalculator.net/mutations/Celestial.webp',
    multiplier: 120,
    stackBonus: 119,
    type: 'Environmental',
    description: 'Occurs during Meteor Shower events. Plants appear slightly discolored with sparkling yellow and purple effects.',
    triggers: 'Occurs during Meteor Shower events'
  },
  {
    id: 'disco',
    name: 'Disco',
    image: 'https://img.growagardencalculator.net/mutations/Disco.webp',
    multiplier: 125,
    stackBonus: 124,
    type: 'Environmental',
    description: 'Triggered during admin-spawned Disco events or via Disco Bee. Plants flash through red, pink, yellow, green, and blue colors instantly, distinct from the gradual color transitions of Rainbow.',
    triggers: 'Triggered during admin-spawned Disco events or via Disco Bee'
  },
  {
    id: 'dawnbound',
    name: 'Dawnbound',
    image: 'https://img.growagardencalculator.net/mutations/Dawnbound.webp',
    multiplier: 150,
    stackBonus: 149,
    type: 'Environmental',
    description: 'Triggered during admin-spawned Sun God Events or with low natural occurrence probability. Sunflowers emit neon yellow light.',
    triggers: 'Triggered during admin-spawned Sun God Events or with low natural occurrence probability',
    constraints: ['sunflower-only']
  },
  {
    id: 'amber',
    name: 'Amber',
    image: 'https://img.growagardencalculator.net/mutations/Amber.webp',
    multiplier: 10,
    stackBonus: 9,
    type: 'Environmental',
    description: 'Applied via an Amber Mutation Spray or occasionally triggered by the Raptor pet during fruit harvest. The crop exhibits a semi-transparent orange coating and releases orange cloud-like particles.',
    triggers: 'Applied via an Amber Mutation Spray or occasionally triggered by the Raptor pet'
  },
  {
    id: 'oldamber',
    name: 'OldAmber',
    image: 'https://img.growagardencalculator.net/mutations/OldAmber.webp',
    multiplier: 20,
    stackBonus: 19,
    type: 'Environmental',
    description: 'Evolves from an Amber mutation after 24 hours of aging. It displays a more pronounced orange hue compared to its initial form.',
    triggers: 'Evolves from an Amber mutation after 24 hours of aging'
  },
  {
    id: 'ancientamber',
    name: 'AncientAmber',
    image: 'https://img.growagardencalculator.net/mutations/AncientAmber.webp',
    multiplier: 50,
    stackBonus: 49,
    type: 'Environmental',
    description: 'Emerges from an OldAmber mutation after an unspecified aging period. Its visual characteristics remain undocumented.',
    triggers: 'Emerges from an OldAmber mutation after an unspecified aging period'
  },
  {
    id: 'cooked',
    name: 'Cooked',
    image: 'https://img.growagardencalculator.net/mutations/Cooked.webp',
    multiplier: 10,
    stackBonus: 9,
    type: 'Environmental',
    description: 'There is a low probability that Cooked Owl will apply this mutation instead of Burnt. Affected plants will exhibit an orange coloration.',
    triggers: 'Low probability application by Cooked Owl instead of Burnt mutation'
  },
  {
    id: 'ceramic',
    name: 'Ceramic',
    image: 'https://img.growagardencalculator.net/mutations/Ceramic.webp',
    multiplier: 30,
    stackBonus: 29,
    type: 'Environmental',
    description: 'Requires the Clay mutation and one of the following: Molten, Sundried, Meteoric, Burnt, Fried, Cooked, or Plasma. Exhibits a darker hue, resembling the Sundried mutation.',
    triggers: 'Requires Clay mutation combined with heat-based mutations'
  },
  {
    id: 'fried',
    name: 'Fried',
    image: 'https://img.growagardencalculator.net/mutations/Fried.webp',
    multiplier: 8,
    stackBonus: 7,
    type: 'Environmental',
    description: 'Triggered during a Fried Chicken event, this mutation is visually identified by small yellow particles that fall from the affected crop.',
    triggers: 'Triggered during a Fried Chicken event'
  },
  {
    id: 'tempestuous',
    name: 'Tempestuous',
    image: 'https://img.growagardencalculator.net/mutations/Tempestuous.webp',
    multiplier: 14,
    stackBonus: 13,
    type: 'Environmental',
    description: 'By combining the Windstruck and Twisted mutations, players can obtain the Tempestuous mutation. With this mutation applied, your fruit will have white wind particle animations.',
    triggers: 'Combination of Windstruck and Twisted mutations'
  },
  {
    id: 'galactic',
    name: 'Galactic',
    image: 'https://img.growagardencalculator.net/mutations/Galactic.webp',
    multiplier: 120,
    stackBonus: 119,
    type: 'Environmental',
    description: 'Triggered during the Space Travel Event, showcasing a light purple or pink hue with neon accents and emitting pink sparkling glimmers.',
    triggers: 'Triggered during the Space Travel Event'
  },
  {
    id: 'voidtouched',
    name: 'Voidtouched',
    image: 'https://img.growagardencalculator.net/mutations/Voidtouched.webp',
    multiplier: 135,
    stackBonus: 134,
    type: 'Environmental',
    description: 'Occurs during admin-spawned Black Hole Events. Plants are surrounded by purple particles resembling miniature black holes, similar to Moonlit effects.',
    triggers: 'Occurs during admin-spawned Black Hole Events'
  },
  {
    id: 'meteoric',
    name: 'Meteoric',
    image: 'https://img.growagardencalculator.net/mutations/Meteoric.webp',
    multiplier: 125,
    stackBonus: 124,
    type: 'Environmental',
    description: 'Triggered during the Meteor Strike event, which can only be initiated by administrators.',
    triggers: 'Triggered during the Meteor Strike event'
  },
  {
    id: 'molten',
    name: 'Molten',
    image: 'https://img.growagardencalculator.net/mutations/Molten.webp',
    multiplier: 25,
    stackBonus: 24,
    type: 'Environmental',
    description: 'During the volcano event, crops may undergo a \'Molten\' mutation. Affected plants will display vibrant orange, yellow, and red hues, with a neon glow akin to the Shocked mutation.',
    triggers: 'During the volcano event'
  },
  {
    id: 'zombified',
    name: 'Zombified',
    image: 'https://img.growagardencalculator.net/mutations/Zombified.webp',
    multiplier: 25,
    stackBonus: 24,
    type: 'Environmental',
    description: 'Caused by interaction with the unobtainable Chicken Zombie. Plants are surrounded by green fog and drip with green liquid.',
    triggers: 'Caused by interaction with the unobtainable Chicken Zombie'
  },
  {
    id: 'friendbound',
    name: 'Friendbound',
    image: 'https://img.growagardencalculator.net/mutations/Friendbound.webp',
    multiplier: 70,
    stackBonus: 69,
    type: 'Environmental',
    description: 'To obtain the Friendbound mutation, purchase the Friendship Pot from the Gear Shop and connect it with an active friend. This mutation causes your fruit to turn pink, emitting pink hearts and stars.',
    triggers: 'Purchase Friendship Pot and connect with an active friend'
  },
  {
    id: 'infected',
    name: 'Infected',
    image: 'https://img.growagardencalculator.net/mutations/Infected.webp',
    multiplier: 75,
    stackBonus: 74,
    type: 'Environmental',
    description: 'Triggered during the Jandel Zombie event. This mutation will emit small, hazy green particles.',
    triggers: 'Triggered during the Jandel Zombie event'
  },
  {
    id: 'sundried',
    name: 'Sundried',
    image: 'https://img.growagardencalculator.net/mutations/Sundried.webp',
    multiplier: 85,
    stackBonus: 84,
    type: 'Environmental',
    description: 'Triggered during the Heatwave event. Apply deep brown hue.',
    triggers: 'Triggered during the Heatwave event'
  },
  {
    id: 'aurora',
    name: 'Aurora',
    image: 'https://img.growagardencalculator.net/mutations/Aurora.webp',
    multiplier: 90,
    stackBonus: 89,
    type: 'Environmental',
    description: 'This mutation is conferred upon crops during the Aurora Borealis event. The affected crop pulsates between shades of blue and purple and emits a faint smoke from its upper portion.',
    triggers: 'Conferred during the Aurora Borealis event'
  },
  {
    id: 'paradisal',
    name: 'Paradisal',
    image: 'https://img.growagardencalculator.net/mutations/Paradisal.webp',
    multiplier: 100,
    stackBonus: 99,
    type: 'Environmental',
    description: 'Triggered by combining Verdant and Sundried mutations on the crop. Features vibrant tropical colors with lush, paradise-inspired effects.',
    triggers: 'Combination of Verdant and Sundried mutations'
  },
  {
    id: 'alienlike',
    name: 'Alienlike',
    image: 'https://img.growagardencalculator.net/mutations/Alienlike.webp',
    multiplier: 100,
    stackBonus: 99,
    type: 'Environmental',
    description: 'Activated during a Alien Invasion Event. Displaying a cyan hue with cyan particles emanating from the fruit, which may have fully or partially transparent sections.',
    triggers: 'Activated during a Alien Invasion Event'
  },
  {
    id: 'dawnbound',
    name: 'Dawnbound',
    image: 'https://img.growagardencalculator.net/mutations/Dawnbound.webp',
    multiplier: 150,
    stackBonus: 149,
    type: 'Environmental',
    description: 'Triggered during admin-spawned Sun God Events or with a low natural occurrence probability. Sunflowers with this mutation glow in neon yellow.',
    triggers: 'Triggered during admin-spawned Sun God Events or with low natural occurrence probability'
  }
];

// All mutations
export const allMutations: Mutation[] = [
  ...growthMutations,
  ...temperatureMutations,
  ...environmentalMutations
];

// Mutation constraint rules
export const mutationConstraints: MutationConstraint[] = [
  {
    group: 'growth',
    mutationIds: ['golden', 'rainbow'],
    maxAllowed: 1,
    description: 'Only one growth mutation can be applied (Golden or Rainbow)'
  },
  {
    group: 'temperature',
    mutationIds: ['wet', 'chilled', 'drenched', 'frozen'],
    maxAllowed: 1,
    description: 'Only one temperature mutation can be applied'
  },
  {
    group: 'cooking',
    mutationIds: ['burnt', 'cooked'],
    maxAllowed: 1,
    description: 'Only one cooking mutation can be applied'
  },
  {
    group: 'amber',
    mutationIds: ['amber', 'oldamber', 'ancientamber'],
    maxAllowed: 1,
    description: 'Only one amber mutation can be applied'
  },
  {
    group: 'earth',
    mutationIds: ['sandy', 'clay'],
    maxAllowed: 1,
    description: 'Only one earth mutation can be applied'
  },
  {
    group: 'ceramic',
    mutationIds: ['clay', 'ceramic'],
    maxAllowed: 1,
    description: 'Only one ceramic mutation can be applied'
  },
  {
    group: 'extreme',
    mutationIds: ['shocked', 'celestial', 'disco', 'voidtouched'],
    maxAllowed: 1,
    description: 'Only one extreme mutation can be applied'
  }
];

// Get mutation by ID
export const getMutationById = (id: string): Mutation | undefined => {
  return allMutations.find(mutation => mutation.id === id);
};

// Get mutations by type
export const getMutationsByType = (type: string): Mutation[] => {
  switch (type) {
    case 'Growth':
      return growthMutations;
    case 'Temperature':
      return temperatureMutations;
    case 'Environmental':
      return environmentalMutations;
    default:
      return allMutations;
  }
};

// Check mutation compatibility
export const checkMutationCompatibility = (selectedMutations: Mutation[]): boolean => {
  for (const constraint of mutationConstraints) {
    const selectedInGroup = selectedMutations.filter(m =>
      constraint.mutationIds.includes(m.id)
    );
    if (selectedInGroup.length > constraint.maxAllowed) {
      return false;
    }
  }
  return true;
};
