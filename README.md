# Grow A Garden Calculator

The ultimate crop value calculator designed for Roblox Grow A Garden players. Calculate precise crop values with mutations, weights, and friend boosts.

## 🌟 Features

### Core Features
- **Crop Value Calculator** - Calculate precise crop values with various mutations
- **Crops Database** - Complete database with 100+ crops
- **Mutations System** - Detailed mutation types and compatibility rules
- **Real-time Calculation** - Support for weight, quantity, friend boost calculations

### Calculation Features
- Support for all crop tiers (Common to Transcendent)
- Complete mutation system (Growth, Temperature, Environmental)
- Smart mutation compatibility checking
- Weight impact on value calculation
- Friend boost calculation

## 🛠️ Tech Stack

- **Frontend Framework**: Next.js 14 (App Router)
- **Styling**: TailwindCSS + Shadcn/ui
- **Type Safety**: TypeScript
- **State Management**: React Hooks + Context API
- **Image Optimization**: Next.js Image component

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router页面
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 主页计算器
│   ├── crops/             # 作物数据库页面
│   └── mutations/         # 变异系统页面
├── components/            # React组件
│   ├── calculator/        # 计算器相关组件
│   ├── crops/            # 作物相关组件
│   ├── mutations/        # 变异相关组件
│   ├── layout/           # 布局组件
│   └── ui/               # 基础UI组件
├── data/                 # 数据文件
│   ├── crops.ts          # 作物数据
│   └── mutations.ts      # 变异数据
├── lib/                  # 工具函数
│   ├── calculator.ts     # 计算引擎
│   └── utils.ts          # 通用工具
└── types/                # TypeScript类型定义
    └── index.ts
```

## 🚀 快速开始

### 环境要求
- Node.js 18+
- Yarn 或 npm

### 安装依赖
```bash
yarn install
# 或
npm install
```

### 启动开发服务器
```bash
yarn dev
# 或
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本
```bash
yarn build
# 或
npm run build
```

## 📊 数据说明

### 作物数据
- 124+ 种作物，涵盖所有稀有度
- 包含种子价格、最小价值、最小重量等属性
- 支持单次和多次收获类型

### 变异数据
- 40+ 种变异类型
- 分为成长、温度、环境三大类
- 包含倍数、堆叠规则、触发条件等信息

### 计算公式
```
总价值 = 基础价值 × 总倍数 × 数量
总倍数 = 变异倍数 × (1 + 朋友加成%)
变异倍数 = 成长变异倍数 × (1 + 环境变异堆叠奖励总和)
```

## 🎮 使用指南

### 主页计算器
1. 选择作物 - 从124+种作物中选择
2. 设置参数 - 输入重量、数量、朋友加成
3. 选择变异 - 根据兼容性规则选择变异
4. 查看结果 - 实时显示计算结果和详细信息

### 作物数据库
- 浏览所有作物信息
- 按稀有度、收获类型筛选
- 查看作物统计数据

### 变异系统
- 了解所有变异类型
- 查看触发条件和约束规则
- 按类型和倍数筛选

## 🔧 开发说明

### 添加新作物
在 `src/data/crops.ts` 中添加新的作物数据：

```typescript
{
  id: 'new-crop',
  name: 'New Crop',
  image: 'https://img.growagardencalculator.net/crops/NewCrop.webp',
  shecklePrice: 1000,
  robuxPrice: 50,
  minimumValue: 100,
  minimumWeight: 1.0,
  tier: 'Rare',
  harvestType: 'Single',
  obtainable: true,
  k: 100
}
```

### 添加新变异
在 `src/data/mutations.ts` 中添加新的变异数据：

```typescript
{
  id: 'new-mutation',
  name: 'New Mutation',
  image: 'https://img.growagardencalculator.net/mutations/NewMutation.webp',
  multiplier: 5,
  stackBonus: 4,
  type: 'Environmental',
  description: '变异描述',
  triggers: '触发条件'
}
```

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details

## 🤝 Contributing

Welcome to submit Issues and Pull Requests to improve this project!

---

Made with ❤️ for Grow A Garden players
