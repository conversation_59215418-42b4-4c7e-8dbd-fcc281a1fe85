import { Crop, Mutation, CalculatorInput, CalculationResult } from '@/types';
import { checkMutationCompatibility } from '@/data/mutations';

/**
 * Core function to calculate crop value
 * Implemented based on the reference website's calculation formula
 */
export function calculateCropValue(input: CalculatorInput): CalculationResult {
  const { crop, weight, quantity, friendBoost, selectedMutations } = input;

  if (!crop) {
    return {
      totalValue: 0,
      totalMultiplier: 0,
      baseValue: 0,
      mutationMultiplier: 0,
      details: {
        cropName: '',
        weight: 0,
        quantity: 0,
        friendBoost: 0,
        mutations: []
      }
    };
  }

  // Check mutation compatibility
  if (!checkMutationCompatibility(selectedMutations)) {
    throw new Error('Selected mutation combination is not compatible');
  }

  // Calculate base value
  let baseValue: number;

  if (weight <= crop.minimumWeight) {
    // When weight is less than or equal to minimum weight, use minimum value
    baseValue = crop.minimumValue;
  } else {
    // When weight exceeds minimum weight, use complex formula: k × Weight²
    const k = crop.k || 1;
    baseValue = k * Math.pow(weight, 2);
  }

  // Calculate mutation multiplier
  const mutationMultiplier = calculateMutationMultiplier(selectedMutations);

  // Calculate friend boost (convert percentage to multiplier)
  const friendMultiplier = 1 + (friendBoost / 100);

  // Calculate total multiplier
  const totalMultiplier = mutationMultiplier * friendMultiplier;

  // Calculate single crop value
  const singleCropValue = baseValue * totalMultiplier;

  // Calculate total value (considering quantity)
  const totalValue = singleCropValue * quantity;

  return {
    totalValue: Math.round(totalValue),
    totalMultiplier: Number(totalMultiplier.toFixed(2)),
    baseValue: Math.round(baseValue),
    mutationMultiplier: Number(mutationMultiplier.toFixed(2)),
    details: {
      cropName: crop.name,
      weight: weight,
      quantity: quantity,
      friendBoost: friendBoost,
      mutations: selectedMutations.map(m => m.name)
    }
  };
}

/**
 * Calculate mutation multiplier
 * Formula: Growth Mutation × (1 + Sum of Environmental Stack Bonuses)
 */
function calculateMutationMultiplier(mutations: Mutation[]): number {
  if (mutations.length === 0) {
    return 1;
  }

  // Separate growth and environmental mutations
  const growthMutations = mutations.filter(m => m.type === 'Growth');
  const environmentalMutations = mutations.filter(m => m.type !== 'Growth');

  // Growth mutation multiplier (only one allowed)
  const growthMultiplier = growthMutations.length > 0 ? growthMutations[0].multiplier : 1;

  // Sum of environmental mutation stack bonuses
  const environmentalStackBonus = environmentalMutations.reduce((sum, mutation) => {
    return sum + mutation.stackBonus;
  }, 0);

  // Total multiplier = Growth mutation multiplier × (1 + Sum of environmental stack bonuses)
  return growthMultiplier * (1 + environmentalStackBonus);
}

/**
 * Format number display (add thousand separators)
 */
export function formatNumber(num: number): string {
  if (num >= 1e12) {
    return (num / 1e12).toFixed(1) + 'T';
  } else if (num >= 1e9) {
    return (num / 1e9).toFixed(1) + 'B';
  } else if (num >= 1e6) {
    return (num / 1e6).toFixed(1) + 'M';
  } else if (num >= 1e3) {
    return (num / 1e3).toFixed(1) + 'K';
  } else {
    return num.toLocaleString();
  }
}

/**
 * Format Sheckles currency display
 */
export function formatSheckles(amount: number): string {
  return `¢${formatNumber(amount)}`;
}

/**
 * Calculate maximum possible multiplier for mutation combination
 */
export function calculateMaxMultiplier(mutations: Mutation[]): number {
  if (mutations.length === 0) return 1;

  // Find the highest growth mutation
  const maxGrowthMultiplier = Math.max(
    ...mutations.filter(m => m.type === 'Growth').map(m => m.multiplier),
    1
  );

  // Calculate all environmental mutation stack bonuses
  const totalEnvironmentalBonus = mutations
    .filter(m => m.type !== 'Growth')
    .reduce((sum, m) => sum + m.stackBonus, 0);

  return maxGrowthMultiplier * (1 + totalEnvironmentalBonus);
}

/**
 * Validate input data
 */
export function validateCalculatorInput(input: CalculatorInput): string[] {
  const errors: string[] = [];

  if (!input.crop) {
    errors.push('Please select a crop');
  }

  if (input.weight <= 0) {
    errors.push('Weight must be greater than 0');
  }

  if (input.quantity <= 0) {
    errors.push('Quantity must be greater than 0');
  }

  if (input.friendBoost < 0 || input.friendBoost > 1000) {
    errors.push('Friend boost must be between 0-1000%');
  }

  if (!checkMutationCompatibility(input.selectedMutations)) {
    errors.push('Selected mutation combination is not compatible');
  }

  return errors;
}

/**
 * Get recommended crop weight (based on minimum weight)
 */
export function getRecommendedWeight(crop: Crop): number {
  return crop.minimumWeight * 1.5; // Recommended weight is 1.5 times the minimum weight
}

/**
 * Calculate weight impact on value
 */
export function calculateWeightImpact(crop: Crop, weight: number): {
  isOptimal: boolean;
  suggestion: string;
} {
  if (weight < crop.minimumWeight) {
    return {
      isOptimal: false,
      suggestion: `Weight below minimum weight ${crop.minimumWeight}kg, increase weight for higher value`
    };
  } else if (weight === crop.minimumWeight) {
    return {
      isOptimal: false,
      suggestion: `Currently at minimum weight, increasing weight can significantly improve value`
    };
  } else {
    return {
      isOptimal: true,
      suggestion: `Weight exceeds minimum weight, value grows quadratically`
    };
  }
}
