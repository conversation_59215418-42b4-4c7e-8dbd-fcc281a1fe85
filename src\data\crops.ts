import { Crop } from '@/types';

export const crops: Crop[] = [
  // Common Tier
  {
    id: 'carrot',
    name: 'Carrot',
    image: 'https://img.growagardencalculator.net/crops/Carrot.webp',
    shecklePrice: 10,
    robuxPrice: 7,
    minimumValue: 18,
    minimumWeight: 0.24,
    tier: 'Common',
    harvestType: 'Single',
    obtainable: true,
    k: 312.5
  },
  {
    id: 'strawberry',
    name: 'Strawberry',
    image: 'https://img.growagardencalculator.net/crops/Strawberry.webp',
    shecklePrice: 50,
    robuxPrice: 21,
    minimumValue: 14,
    minimumWeight: 0.29,
    tier: 'Common',
    harvestType: 'Multi',
    obtainable: true,
    k: 166.4
  },
  {
    id: 'chocolate-carrot',
    name: 'Chocolate Carrot',
    image: 'https://img.growagardencalculator.net/crops/Chocolate-Carrot.webp',
    shecklePrice: 75,
    robuxPrice: 25,
    minimumValue: 20,
    minimumWeight: 0.32,
    tier: 'Common',
    harvestType: 'Single',
    obtainable: true,
    k: 195.3
  },
  {
    id: 'pink-tulip',
    name: '<PERSON> Tulip',
    image: 'https://img.growagardencalculator.net/crops/Pink-Tulip.webp',
    shecklePrice: 100,
    robuxPrice: 29,
    minimumValue: 25,
    minimumWeight: 0.35,
    tier: 'Common',
    harvestType: 'Single',
    obtainable: true,
    k: 204.1
  },

  // Uncommon Tier
  {
    id: 'blueberry',
    name: 'Blueberry',
    image: 'https://img.growagardencalculator.net/crops/Blueberry.webp',
    shecklePrice: 400,
    robuxPrice: 49,
    minimumValue: 18,
    minimumWeight: 0.17,
    tier: 'Uncommon',
    harvestType: 'Multi',
    obtainable: true,
    k: 623.5
  },
  {
    id: 'orange-tulip',
    name: 'Orange Tulip',
    image: 'https://img.growagardencalculator.net/crops/Orange-Tulip.webp',
    shecklePrice: 600,
    robuxPrice: 14,
    minimumValue: 751,
    minimumWeight: 0.05,
    tier: 'Uncommon',
    harvestType: 'Single',
    obtainable: true,
    k: 300400
  },
  {
    id: 'lavender',
    name: 'Lavender',
    image: 'https://img.growagardencalculator.net/crops/Lavender.webp',
    shecklePrice: 650,
    robuxPrice: 16,
    minimumValue: 800,
    minimumWeight: 0.06,
    tier: 'Uncommon',
    harvestType: 'Single',
    obtainable: true,
    k: 222222
  },
  {
    id: 'stonebite',
    name: 'Stonebite',
    image: 'https://img.growagardencalculator.net/crops/Stonebite.webp',
    shecklePrice: 700,
    robuxPrice: 18,
    minimumValue: 850,
    minimumWeight: 0.07,
    tier: 'Uncommon',
    harvestType: 'Single',
    obtainable: true,
    k: 173469
  },
  {
    id: 'crocus',
    name: 'Crocus',
    image: 'https://img.growagardencalculator.net/crops/Crocus.webp',
    shecklePrice: 750,
    robuxPrice: 20,
    minimumValue: 900,
    minimumWeight: 0.08,
    tier: 'Uncommon',
    harvestType: 'Single',
    obtainable: true,
    k: 140625
  },
  {
    id: 'wild-carrot',
    name: 'Wild Carrot',
    image: 'https://img.growagardencalculator.net/crops/Wild-Carrot.webp',
    shecklePrice: 800,
    robuxPrice: 22,
    minimumValue: 950,
    minimumWeight: 0.09,
    tier: 'Uncommon',
    harvestType: 'Single',
    obtainable: true,
    k: 117284
  },
  {
    id: 'rose',
    name: 'Rose',
    image: 'https://img.growagardencalculator.net/crops/Rose.webp',
    shecklePrice: 850,
    robuxPrice: 24,
    minimumValue: 1000,
    minimumWeight: 0.10,
    tier: 'Uncommon',
    harvestType: 'Single',
    obtainable: true,
    k: 100000
  },
  {
    id: 'nightshade',
    name: 'Nightshade',
    image: 'https://img.growagardencalculator.net/crops/Nightshade.webp',
    shecklePrice: 900,
    robuxPrice: 26,
    minimumValue: 1050,
    minimumWeight: 0.11,
    tier: 'Uncommon',
    harvestType: 'Multi',
    obtainable: true,
    k: 86777
  },
  {
    id: 'red-lollipop',
    name: 'Red Lollipop',
    image: 'https://img.growagardencalculator.net/crops/Red-Lollipop.webp',
    shecklePrice: 950,
    robuxPrice: 28,
    minimumValue: 1100,
    minimumWeight: 0.12,
    tier: 'Uncommon',
    harvestType: 'Single',
    obtainable: true,
    k: 76389
  },
  {
    id: 'manuka-flower',
    name: 'Manuka Flower',
    image: 'https://img.growagardencalculator.net/crops/Manuka-Flower.webp',
    shecklePrice: 1000,
    robuxPrice: 30,
    minimumValue: 1150,
    minimumWeight: 0.13,
    tier: 'Uncommon',
    harvestType: 'Single',
    obtainable: true,
    k: 68047
  },
  {
    id: 'blue-lollipop',
    name: 'Blue Lollipop',
    image: 'https://img.growagardencalculator.net/crops/Blue-Lollipop.webp',
    shecklePrice: 1050,
    robuxPrice: 32,
    minimumValue: 1200,
    minimumWeight: 0.14,
    tier: 'Uncommon',
    harvestType: 'Single',
    obtainable: true,
    k: 61224
  },

  // Rare Tier
  {
    id: 'tomato',
    name: 'Tomato',
    image: 'https://img.growagardencalculator.net/crops/Tomato.webp',
    shecklePrice: 800,
    robuxPrice: 79,
    minimumValue: 27,
    minimumWeight: 0.44,
    tier: 'Rare',
    harvestType: 'Multi',
    obtainable: true,
    k: 139.7
  },
  {
    id: 'corn',
    name: 'Corn',
    image: 'https://img.growagardencalculator.net/crops/Corn.webp',
    shecklePrice: 1300,
    robuxPrice: 135,
    minimumValue: 36,
    minimumWeight: 1.9,
    tier: 'Rare',
    harvestType: 'Multi',
    obtainable: true,
    k: 9.97
  },
  {
    id: 'daffodil',
    name: 'Daffodil',
    image: 'https://img.growagardencalculator.net/crops/Daffodil.webp',
    shecklePrice: 1000,
    robuxPrice: 19,
    minimumValue: 903,
    minimumWeight: 0.16,
    tier: 'Rare',
    harvestType: 'Single',
    obtainable: true,
    k: 35273.4
  },
  {
    id: 'watermelon',
    name: 'Watermelon',
    image: 'https://img.growagardencalculator.net/crops/Watermelon.webp',
    shecklePrice: 2500,
    robuxPrice: 195,
    minimumValue: 2708,
    minimumWeight: 7.3,
    tier: 'Rare',
    harvestType: 'Single',
    obtainable: true,
    k: 50.8
  },
  {
    id: 'liberty-lily',
    name: 'Liberty Lily',
    image: 'https://img.growagardencalculator.net/crops/Liberty-Lily.webp',
    shecklePrice: 2700,
    robuxPrice: 205,
    minimumValue: 2850,
    minimumWeight: 7.6,
    tier: 'Rare',
    harvestType: 'Single',
    obtainable: true,
    k: 49.3
  },
  {
    id: 'nectarshade',
    name: 'Nectarshade',
    image: 'https://img.growagardencalculator.net/crops/Nectarshade.webp',
    shecklePrice: 2900,
    robuxPrice: 215,
    minimumValue: 3000,
    minimumWeight: 7.9,
    tier: 'Rare',
    harvestType: 'Multi',
    obtainable: true,
    k: 48.1
  },
  {
    id: 'delphinium',
    name: 'Delphinium',
    image: 'https://img.growagardencalculator.net/crops/Delphinium.webp',
    shecklePrice: 3100,
    robuxPrice: 225,
    minimumValue: 3150,
    minimumWeight: 8.2,
    tier: 'Rare',
    harvestType: 'Single',
    obtainable: true,
    k: 46.9
  },
  {
    id: 'paradise-petal',
    name: 'Paradise Petal',
    image: 'https://img.growagardencalculator.net/crops/Paradise-Petal.webp',
    shecklePrice: 3300,
    robuxPrice: 235,
    minimumValue: 3300,
    minimumWeight: 8.5,
    tier: 'Rare',
    harvestType: 'Single',
    obtainable: true,
    k: 45.8
  },
  {
    id: 'succulent',
    name: 'Succulent',
    image: 'https://img.growagardencalculator.net/crops/Succulent.webp',
    shecklePrice: 3500,
    robuxPrice: 245,
    minimumValue: 3450,
    minimumWeight: 8.8,
    tier: 'Rare',
    harvestType: 'Multi',
    obtainable: true,
    k: 44.5
  },
  {
    id: 'pear',
    name: 'Pear',
    image: 'https://img.growagardencalculator.net/crops/Pear.webp',
    shecklePrice: 3700,
    robuxPrice: 255,
    minimumValue: 3600,
    minimumWeight: 9.1,
    tier: 'Rare',
    harvestType: 'Multi',
    obtainable: true,
    k: 43.5
  },
  {
    id: 'foxglove',
    name: 'Foxglove',
    image: 'https://img.growagardencalculator.net/crops/Foxglove.webp',
    shecklePrice: 3900,
    robuxPrice: 265,
    minimumValue: 3750,
    minimumWeight: 9.4,
    tier: 'Rare',
    harvestType: 'Single',
    obtainable: true,
    k: 42.4
  },
  {
    id: 'glowshroom',
    name: 'Glowshroom',
    image: 'https://img.growagardencalculator.net/crops/Glowshroom.webp',
    shecklePrice: 4100,
    robuxPrice: 275,
    minimumValue: 3900,
    minimumWeight: 9.7,
    tier: 'Rare',
    harvestType: 'Single',
    obtainable: true,
    k: 41.4
  },
  {
    id: 'mint',
    name: 'Mint',
    image: 'https://img.growagardencalculator.net/crops/Mint.webp',
    shecklePrice: 4300,
    robuxPrice: 285,
    minimumValue: 4050,
    minimumWeight: 10.0,
    tier: 'Rare',
    harvestType: 'Multi',
    obtainable: true,
    k: 40.5
  },
  {
    id: 'raspberry',
    name: 'Raspberry',
    image: 'https://img.growagardencalculator.net/crops/Raspberry.webp',
    shecklePrice: 4500,
    robuxPrice: 295,
    minimumValue: 4200,
    minimumWeight: 10.3,
    tier: 'Rare',
    harvestType: 'Multi',
    obtainable: true,
    k: 39.6
  },

  // Legendary Tier
  {
    id: 'pumpkin',
    name: 'Pumpkin',
    image: 'https://img.growagardencalculator.net/crops/Pumpkin.webp',
    shecklePrice: 3000,
    robuxPrice: 210,
    minimumValue: 3069,
    minimumWeight: 6.9,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 64.5
  },
  {
    id: 'apple',
    name: 'Apple',
    image: 'https://img.growagardencalculator.net/crops/Apple.webp',
    shecklePrice: 3250,
    robuxPrice: 390,
    minimumValue: 248,
    minimumWeight: 2.85,
    tier: 'Legendary',
    harvestType: 'Multi',
    obtainable: true,
    k: 30.5
  },
  {
    id: 'bamboo',
    name: 'Bamboo',
    image: 'https://img.growagardencalculator.net/crops/Bamboo.webp',
    shecklePrice: 4000,
    robuxPrice: 99,
    minimumValue: 3610,
    minimumWeight: 3.8,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 249.7
  },
  {
    id: 'firework-flower',
    name: 'Firework Flower',
    image: 'https://img.growagardencalculator.net/crops/Firework-Flower.webp',
    shecklePrice: 4500,
    robuxPrice: 109,
    minimumValue: 3900,
    minimumWeight: 4.1,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 232.0
  },
  {
    id: 'horned-dinoshroom',
    name: 'Horned Dinoshroom',
    image: 'https://img.growagardencalculator.net/crops/Horned-Dinoshroom.webp',
    shecklePrice: 5000,
    robuxPrice: 119,
    minimumValue: 4200,
    minimumWeight: 4.4,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 217.0
  },
  {
    id: 'boneboo',
    name: 'Boneboo',
    image: 'https://img.growagardencalculator.net/crops/Boneboo.webp',
    shecklePrice: 5500,
    robuxPrice: 129,
    minimumValue: 4500,
    minimumWeight: 4.7,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 203.8
  },
  {
    id: 'violet-corn',
    name: 'Violet Corn',
    image: 'https://img.growagardencalculator.net/crops/Violet-Corn.webp',
    shecklePrice: 6000,
    robuxPrice: 139,
    minimumValue: 4800,
    minimumWeight: 5.0,
    tier: 'Legendary',
    harvestType: 'Multi',
    obtainable: true,
    k: 192.0
  },
  {
    id: 'cantaloupe',
    name: 'Cantaloupe',
    image: 'https://img.growagardencalculator.net/crops/Cantaloupe.webp',
    shecklePrice: 6500,
    robuxPrice: 149,
    minimumValue: 5100,
    minimumWeight: 5.3,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 181.5
  },
  {
    id: 'lilac',
    name: 'Lilac',
    image: 'https://img.growagardencalculator.net/crops/Lilac.webp',
    shecklePrice: 7000,
    robuxPrice: 159,
    minimumValue: 5400,
    minimumWeight: 5.6,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 172.3
  },
  {
    id: 'moonflower',
    name: 'Moonflower',
    image: 'https://img.growagardencalculator.net/crops/Moonflower.webp',
    shecklePrice: 7500,
    robuxPrice: 169,
    minimumValue: 5700,
    minimumWeight: 5.9,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 163.9
  },
  {
    id: 'starfruit',
    name: 'Starfruit',
    image: 'https://img.growagardencalculator.net/crops/Starfruit.webp',
    shecklePrice: 8000,
    robuxPrice: 179,
    minimumValue: 6000,
    minimumWeight: 6.2,
    tier: 'Legendary',
    harvestType: 'Multi',
    obtainable: true,
    k: 156.3
  },
  {
    id: 'papaya',
    name: 'Papaya',
    image: 'https://img.growagardencalculator.net/crops/Papaya.webp',
    shecklePrice: 8500,
    robuxPrice: 189,
    minimumValue: 6300,
    minimumWeight: 6.5,
    tier: 'Legendary',
    harvestType: 'Multi',
    obtainable: true,
    k: 149.2
  },
  {
    id: 'cranberry',
    name: 'Cranberry',
    image: 'https://img.growagardencalculator.net/crops/Cranberry.webp',
    shecklePrice: 9000,
    robuxPrice: 199,
    minimumValue: 6600,
    minimumWeight: 6.8,
    tier: 'Legendary',
    harvestType: 'Multi',
    obtainable: true,
    k: 142.9
  },
  {
    id: 'durian',
    name: 'Durian',
    image: 'https://img.growagardencalculator.net/crops/Durian.webp',
    shecklePrice: 9500,
    robuxPrice: 209,
    minimumValue: 6900,
    minimumWeight: 7.1,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 137.0
  },
  {
    id: 'rafflesia',
    name: 'Rafflesia',
    image: 'https://img.growagardencalculator.net/crops/Rafflesia.webp',
    shecklePrice: 10000,
    robuxPrice: 219,
    minimumValue: 7200,
    minimumWeight: 7.4,
    tier: 'Legendary',
    harvestType: 'Single',
    obtainable: true,
    k: 131.6
  },

  // Mythical Tier
  {
    id: 'coconut',
    name: 'Coconut',
    image: 'https://img.growagardencalculator.net/crops/Coconut.webp',
    shecklePrice: 6000,
    robuxPrice: 435,
    minimumValue: 361,
    minimumWeight: 13.31,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 2.04
  },
  {
    id: 'cactus',
    name: 'Cactus',
    image: 'https://img.growagardencalculator.net/crops/Cactus.webp',
    shecklePrice: 15000,
    robuxPrice: 497,
    minimumValue: 3069,
    minimumWeight: 6.65,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 69.4
  },
  {
    id: 'dragon-fruit',
    name: 'Dragon Fruit',
    image: 'https://img.growagardencalculator.net/crops/Dragon-Fruit.webp',
    shecklePrice: 50000,
    robuxPrice: 597,
    minimumValue: 4287,
    minimumWeight: 11.38,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 33.1
  },
  {
    id: 'mango',
    name: 'Mango',
    image: 'https://img.growagardencalculator.net/crops/Mango.webp',
    shecklePrice: 100000,
    robuxPrice: 580,
    minimumValue: 5866,
    minimumWeight: 14.28,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 28.8
  },
  {
    id: 'nectarine',
    name: 'Nectarine',
    image: 'https://img.growagardencalculator.net/crops/Nectarine.webp',
    shecklePrice: 120000,
    robuxPrice: 590,
    minimumValue: 6200,
    minimumWeight: 15.1,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 27.2
  },
  {
    id: 'celestiberry',
    name: 'Celestiberry',
    image: 'https://img.growagardencalculator.net/crops/Celestiberry.webp',
    shecklePrice: 150000,
    robuxPrice: 610,
    minimumValue: 6800,
    minimumWeight: 16.5,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 25.0
  },
  {
    id: 'blood-banana',
    name: 'Blood Banana',
    image: 'https://img.growagardencalculator.net/crops/Blood-Banana.webp',
    shecklePrice: 180000,
    robuxPrice: 630,
    minimumValue: 7500,
    minimumWeight: 18.0,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 23.1
  },
  {
    id: 'moon-melon',
    name: 'Moon Melon',
    image: 'https://img.growagardencalculator.net/crops/Moon-Melon.webp',
    shecklePrice: 220000,
    robuxPrice: 650,
    minimumValue: 8300,
    minimumWeight: 19.8,
    tier: 'Mythical',
    harvestType: 'Single',
    obtainable: true,
    k: 21.2
  },
  {
    id: 'lily-of-the-valley',
    name: 'Lily Of The Valley',
    image: 'https://img.growagardencalculator.net/crops/Lily-Of-The-Valley.webp',
    shecklePrice: 260000,
    robuxPrice: 670,
    minimumValue: 9200,
    minimumWeight: 21.5,
    tier: 'Mythical',
    harvestType: 'Single',
    obtainable: true,
    k: 19.9
  },
  {
    id: 'firefly-fern',
    name: 'Firefly Fern',
    image: 'https://img.growagardencalculator.net/crops/Firefly-Fern.webp',
    shecklePrice: 300000,
    robuxPrice: 690,
    minimumValue: 10200,
    minimumWeight: 23.4,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 18.6
  },
  {
    id: 'bendboo',
    name: 'Bendboo',
    image: 'https://img.growagardencalculator.net/crops/Bendboo.webp',
    shecklePrice: 350000,
    robuxPrice: 710,
    minimumValue: 11300,
    minimumWeight: 25.6,
    tier: 'Mythical',
    harvestType: 'Single',
    obtainable: true,
    k: 17.2
  },
  {
    id: 'cocovine',
    name: 'Cocovine',
    image: 'https://img.growagardencalculator.net/crops/Cocovine.webp',
    shecklePrice: 400000,
    robuxPrice: 730,
    minimumValue: 12500,
    minimumWeight: 27.9,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 16.1
  },
  {
    id: 'parasol-flower',
    name: 'Parasol Flower',
    image: 'https://img.growagardencalculator.net/crops/Parasol-Flower.webp',
    shecklePrice: 450000,
    robuxPrice: 750,
    minimumValue: 13800,
    minimumWeight: 30.4,
    tier: 'Mythical',
    harvestType: 'Single',
    obtainable: true,
    k: 14.9
  },
  {
    id: 'pink-lily',
    name: 'Pink Lily',
    image: 'https://img.growagardencalculator.net/crops/Pink-Lily.webp',
    shecklePrice: 500000,
    robuxPrice: 770,
    minimumValue: 15200,
    minimumWeight: 33.1,
    tier: 'Mythical',
    harvestType: 'Single',
    obtainable: true,
    k: 13.9
  },
  {
    id: 'purple-dahlia',
    name: 'Purple Dahlia',
    image: 'https://img.growagardencalculator.net/crops/Purple-Dahlia.webp',
    shecklePrice: 550000,
    robuxPrice: 790,
    minimumValue: 16800,
    minimumWeight: 35.9,
    tier: 'Mythical',
    harvestType: 'Single',
    obtainable: true,
    k: 13.0
  },
  {
    id: 'moonglow',
    name: 'Moonglow',
    image: 'https://img.growagardencalculator.net/crops/Moonglow.webp',
    shecklePrice: 600000,
    robuxPrice: 810,
    minimumValue: 18500,
    minimumWeight: 38.9,
    tier: 'Mythical',
    harvestType: 'Multi',
    obtainable: true,
    k: 12.2
  },

  // Divine Tier
  {
    id: 'grape',
    name: 'Grape',
    image: 'https://img.growagardencalculator.net/crops/Grape.webp',
    shecklePrice: 850000,
    robuxPrice: 599,
    minimumValue: 7085,
    minimumWeight: 2.85,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 872.2
  },
  {
    id: 'mushroom',
    name: 'Mushroom',
    image: 'https://img.growagardencalculator.net/crops/Mushroom.webp',
    shecklePrice: 150000,
    robuxPrice: 249,
    minimumValue: 136278,
    minimumWeight: 25.9,
    tier: 'Divine',
    harvestType: 'Single',
    obtainable: true,
    k: 203.2
  },
  {
    id: 'pepper',
    name: 'Pepper',
    image: 'https://img.growagardencalculator.net/crops/Pepper.webp',
    shecklePrice: 1000000,
    robuxPrice: 629,
    minimumValue: 7220,
    minimumWeight: 4.75,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 320.0
  },
  {
    id: 'cacao',
    name: 'Cacao',
    image: 'https://img.growagardencalculator.net/crops/Cacao.webp',
    shecklePrice: 2500000,
    robuxPrice: 679,
    minimumValue: 10830,
    minimumWeight: 7.6,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 187.5
  },
  {
    id: 'hive-fruit',
    name: 'Hive Fruit',
    image: 'https://img.growagardencalculator.net/crops/Hive-Fruit.webp',
    shecklePrice: 3000000,
    robuxPrice: 699,
    minimumValue: 12500,
    minimumWeight: 8.2,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 186.0
  },
  {
    id: 'moon-mango',
    name: 'Moon Mango',
    image: 'https://img.growagardencalculator.net/crops/Moon-Mango.webp',
    shecklePrice: 3500000,
    robuxPrice: 729,
    minimumValue: 14200,
    minimumWeight: 9.1,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 171.5
  },
  {
    id: 'travelers-fruit',
    name: 'Traveler\'s Fruit',
    image: 'https://img.growagardencalculator.net/crops/Travelers-Fruit.webp',
    shecklePrice: 4000000,
    robuxPrice: 759,
    minimumValue: 16800,
    minimumWeight: 10.5,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 152.4
  },
  {
    id: 'fossilight',
    name: 'Fossilight',
    image: 'https://img.growagardencalculator.net/crops/Fossilight.webp',
    shecklePrice: 4500000,
    robuxPrice: 789,
    minimumValue: 18900,
    minimumWeight: 11.8,
    tier: 'Divine',
    harvestType: 'Single',
    obtainable: true,
    k: 135.8
  },
  {
    id: 'dragon-pepper',
    name: 'Dragon Pepper',
    image: 'https://img.growagardencalculator.net/crops/Dragon-Pepper.webp',
    shecklePrice: 5000000,
    robuxPrice: 819,
    minimumValue: 21500,
    minimumWeight: 12.9,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 129.3
  },
  {
    id: 'rosy-delight',
    name: 'Rosy Delight',
    image: 'https://img.growagardencalculator.net/crops/Rosy-Delight.webp',
    shecklePrice: 5500000,
    robuxPrice: 849,
    minimumValue: 24200,
    minimumWeight: 14.1,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 121.7
  },
  {
    id: 'sunflower',
    name: 'Sunflower',
    image: 'https://img.growagardencalculator.net/crops/Sunflower.webp',
    shecklePrice: 6000000,
    robuxPrice: 879,
    minimumValue: 27100,
    minimumWeight: 15.4,
    tier: 'Divine',
    harvestType: 'Single',
    obtainable: true,
    k: 114.3
  },
  {
    id: 'moon-blossom',
    name: 'Moon Blossom',
    image: 'https://img.growagardencalculator.net/crops/Moon-Blossom.webp',
    shecklePrice: 6500000,
    robuxPrice: 909,
    minimumValue: 30200,
    minimumWeight: 16.8,
    tier: 'Divine',
    harvestType: 'Single',
    obtainable: true,
    k: 107.1
  },
  {
    id: 'soul-fruit',
    name: 'Soul Fruit',
    image: 'https://img.growagardencalculator.net/crops/Soul-Fruit.webp',
    shecklePrice: 7000000,
    robuxPrice: 939,
    minimumValue: 33500,
    minimumWeight: 18.2,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 101.1
  },
  {
    id: 'cursed-fruit',
    name: 'Cursed Fruit',
    image: 'https://img.growagardencalculator.net/crops/Cursed-Fruit.webp',
    shecklePrice: 7500000,
    robuxPrice: 969,
    minimumValue: 37000,
    minimumWeight: 19.7,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 95.4
  },
  {
    id: 'lotus',
    name: 'Lotus',
    image: 'https://img.growagardencalculator.net/crops/Lotus.webp',
    shecklePrice: 8000000,
    robuxPrice: 999,
    minimumValue: 40800,
    minimumWeight: 21.3,
    tier: 'Divine',
    harvestType: 'Single',
    obtainable: true,
    k: 89.9
  },
  {
    id: 'venus-fly-trap',
    name: 'Venus Fly Trap',
    image: 'https://img.growagardencalculator.net/crops/Venus-Fly-Trap.webp',
    shecklePrice: 8500000,
    robuxPrice: 1029,
    minimumValue: 44900,
    minimumWeight: 22.9,
    tier: 'Divine',
    harvestType: 'Multi',
    obtainable: true,
    k: 85.7
  },

  // Prismatic Tier
  {
    id: 'beanstalk',
    name: 'Beanstalk',
    image: 'https://img.growagardencalculator.net/crops/Beanstalk.webp',
    shecklePrice: 10000000,
    robuxPrice: 715,
    minimumValue: 25270,
    minimumWeight: 9.5,
    tier: 'Prismatic',
    harvestType: 'Multi',
    obtainable: true,
    k: 280.0
  },
  {
    id: 'ember-lily',
    name: 'Ember Lily',
    image: 'https://img.growagardencalculator.net/crops/Ember-Lily.webp',
    shecklePrice: 15000000,
    robuxPrice: 779,
    minimumValue: 50138,
    minimumWeight: 11.4,
    tier: 'Prismatic',
    harvestType: 'Multi',
    obtainable: true,
    k: 385.7
  },
  {
    id: 'sugar-apple',
    name: 'Sugar Apple',
    image: 'https://img.growagardencalculator.net/crops/Sugar-Apple.webp',
    shecklePrice: 25000000,
    robuxPrice: 819,
    minimumValue: 43320,
    minimumWeight: 8.55,
    tier: 'Prismatic',
    harvestType: 'Multi',
    obtainable: true,
    k: 592.6
  },
  {
    id: 'burning-bud',
    name: 'Burning Bud',
    image: 'https://img.growagardencalculator.net/crops/Burning-Bud.webp',
    shecklePrice: 40000000,
    robuxPrice: 915,
    minimumValue: 63175,
    minimumWeight: 11.4,
    tier: 'Prismatic',
    harvestType: 'Multi',
    obtainable: true,
    k: 486.2
  },
  {
    id: 'giant-pinecone',
    name: 'Giant Pinecone',
    image: 'https://img.growagardencalculator.net/crops/Giant-Pinecone.webp',
    shecklePrice: 55000000,
    robuxPrice: 929,
    minimumValue: 64980,
    minimumWeight: 5.14,
    tier: 'Prismatic',
    harvestType: 'Multi',
    obtainable: true,
    k: 2461.5
  },
  {
    id: 'elephant-ears',
    name: 'Elephant Ears',
    image: 'https://img.growagardencalculator.net/crops/Elephant-Ears.webp',
    shecklePrice: 75000000,
    robuxPrice: 999,
    minimumValue: 85000,
    minimumWeight: 6.2,
    tier: 'Prismatic',
    harvestType: 'Multi',
    obtainable: true,
    k: 2200.0
  },

  // Transcendent Tier
  {
    id: 'bone-blossom',
    name: 'Bone Blossom',
    image: 'https://img.growagardencalculator.net/crops/Bone-Blossom.webp',
    shecklePrice: 100000000,
    robuxPrice: 1299,
    minimumValue: 150000,
    minimumWeight: 8.0,
    tier: 'Transcendent',
    harvestType: 'Multi',
    obtainable: true,
    k: 2343.75
  }
];

// Group crops by tier
export const cropsByTier = crops.reduce((acc, crop) => {
  if (!acc[crop.tier]) {
    acc[crop.tier] = [];
  }
  acc[crop.tier].push(crop);
  return acc;
}, {} as Record<string, Crop[]>);

// Get crop by ID
export const getCropById = (id: string): Crop | undefined => {
  return crops.find(crop => crop.id === id);
};

// Search crops function
export const searchCrops = (query: string): Crop[] => {
  const lowercaseQuery = query.toLowerCase();
  return crops.filter(crop =>
    crop.name.toLowerCase().includes(lowercaseQuery) ||
    crop.tier.toLowerCase().includes(lowercaseQuery)
  );
};
