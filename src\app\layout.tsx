import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { MainLayout } from '@/components/layout/main-layout'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Grow A Garden Calculator - #1 Crop Value Calculator for Roblox',
  description: 'The ultimate Grow A Garden Calculator for Roblox. Calculate crop values with mutations, weights, and friend boosts. Complete database of 100+ crops and 40+ mutations. Free online tool.',
  keywords: 'Grow A Garden Calculator, Roblox Grow A Garden, Crop Calculator, Mutation Calculator, Crop Values, Roblox Calculator, Garden Calculator, Farming Calculator',
  authors: [{ name: 'Grow A Garden Calculator Team' }],
  creator: 'Grow A Garden Calculator',
  publisher: 'Grow A Garden Calculator',
  robots: 'index, follow',
  openGraph: {
    title: 'Grow A Garden Calculator - #1 Crop Value Calculator for Roblox',
    description: 'The ultimate Grow A Garden Calculator for Roblox. Calculate crop values with mutations, weights, and friend boosts. Complete database of 100+ crops and 40+ mutations.',
    url: 'https://growagardencalculator.com',
    siteName: 'Grow A Garden Calculator',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Grow A Garden Calculator - #1 Crop Value Calculator for Roblox',
    description: 'The ultimate Grow A Garden Calculator for Roblox. Calculate crop values with mutations, weights, and friend boosts.',
    creator: '@GrowAGardenCalc',
  },
  alternates: {
    canonical: 'https://growagardencalculator.com',
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <MainLayout>
          {children}
        </MainLayout>
      </body>
    </html>
  )
}
