'use client';

import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Grow A Garden Crops Database - Complete List of All Crops | Grow A Garden Calculator',
  description: 'Complete database of all 100+ crops in Roblox Grow A Garden. View crop tiers, values, weights, and harvest types. Filter by rarity and search crops.',
  keywords: 'Grow A Garden crops, Roblox crops, crop database, crop tiers, crop values, farming guide, Grow A Garden Calculator',
  openGraph: {
    title: 'Grow A Garden Crops Database - Complete List of All Crops',
    description: 'Complete database of all 100+ crops in Roblox Grow A Garden. View crop tiers, values, weights, and harvest types.',
    url: 'https://growagardencalculator.com/crops',
  },
};

import React from 'react';
import { Crop } from '@/types';
import { crops } from '@/data/crops';
import { CropCard } from '@/components/crops/crop-card';
import { CropFilters } from '@/components/crops/crop-filters';
import { CropStats } from '@/components/crops/crop-stats';
import { Button } from '@/components/ui/button';

export default function CropsPage() {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedTier, setSelectedTier] = React.useState('all');
  const [selectedHarvestType, setSelectedHarvestType] = React.useState('all');
  const [showObtainableOnly, setShowObtainableOnly] = React.useState(false);
  const [sortBy, setSortBy] = React.useState<'name' | 'price' | 'value' | 'tier'>('tier');
  const [sortOrder, setSortOrder] = React.useState<'asc' | 'desc'>('asc');

  // Filter crops
  const filteredCrops = React.useMemo(() => {
    let filtered = crops;

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(crop =>
        crop.name.toLowerCase().includes(query)
      );
    }

    // Filter by tier
    if (selectedTier !== 'all') {
      filtered = filtered.filter(crop => crop.tier === selectedTier);
    }

    // Filter by harvest type
    if (selectedHarvestType !== 'all') {
      filtered = filtered.filter(crop => crop.harvestType === selectedHarvestType);
    }

    // Filter by obtainability
    if (showObtainableOnly) {
      filtered = filtered.filter(crop => crop.obtainable);
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'price':
          aValue = a.shecklePrice;
          bValue = b.shecklePrice;
          break;
        case 'value':
          aValue = a.minimumValue;
          bValue = b.minimumValue;
          break;
        case 'tier':
          const tierOrder = ['Common', 'Uncommon', 'Rare', 'Legendary', 'Mythical', 'Divine', 'Prismatic', 'Transcendent'];
          aValue = tierOrder.indexOf(a.tier);
          bValue = tierOrder.indexOf(b.tier);
          break;
        default:
          return 0;
      }

      if (typeof aValue === 'string') {
        return sortOrder === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        return sortOrder === 'asc'
          ? aValue - bValue
          : bValue - aValue;
      }
    });

    return filtered;
  }, [searchQuery, selectedTier, selectedHarvestType, showObtainableOnly, sortBy, sortOrder]);

  // Reset filters
  const resetFilters = () => {
    setSearchQuery('');
    setSelectedTier('all');
    setSelectedHarvestType('all');
    setShowObtainableOnly(false);
    setSortBy('tier');
    setSortOrder('asc');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Title */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Crops Database</h1>
        <p className="text-muted-foreground">Browse all crops in Grow A Garden</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Side: Filters and Stats */}
        <div className="lg:col-span-1 space-y-6">
          <CropFilters
            searchQuery={searchQuery}
            selectedTier={selectedTier}
            selectedHarvestType={selectedHarvestType}
            showObtainableOnly={showObtainableOnly}
            onSearchChange={setSearchQuery}
            onTierChange={setSelectedTier}
            onHarvestTypeChange={setSelectedHarvestType}
            onObtainableToggle={setShowObtainableOnly}
            onReset={resetFilters}
          />

          <CropStats crops={crops} filteredCrops={filteredCrops} />
        </div>

        {/* Right Side: Crop List */}
        <div className="lg:col-span-3">
          {/* Sort Options */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="tier">Tier</option>
                <option value="name">Name</option>
                <option value="price">Seed Price</option>
                <option value="value">Min Value</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="text-sm px-2 py-1 border rounded hover:bg-muted"
              >
                {sortOrder === 'asc' ? 'Ascending' : 'Descending'}
              </button>
            </div>
            <div className="text-sm text-muted-foreground">
              Showing {filteredCrops.length} / {crops.length} crops
            </div>
          </div>

          {/* Crop Grid */}
          {filteredCrops.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
              {filteredCrops.map((crop) => (
                <CropCard key={crop.id} crop={crop} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-muted-foreground mb-4">
                No matching crops found
              </div>
              <Button onClick={resetFilters} variant="outline">
                Reset Filters
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
