'use client';

import React from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Github, ExternalLink, Heart } from 'lucide-react';

export function Footer() {
  const currentYear = new Date().getFullYear();

  const links = {
    main: [
      { name: 'Calculator', href: '/' },
      { name: 'Crops Database', href: '/crops' },
      { name: 'Mutations Guide', href: '/mutations' }
    ]
  };

  return (
    <footer className="border-t bg-muted/30 mt-12">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo和描述 */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-lg">G</span>
              </div>
              <div>
                <span className="font-bold text-lg">Grow A Garden Calculator</span>
              </div>
            </div>
            <p className="text-muted-foreground text-sm leading-relaxed mb-4">
              The ultimate crop value calculator designed for Roblox Grow A Garden players.
              Calculate precise crop values with mutations, weights, and friend boosts to optimize your farming strategy.
            </p>
            <div className="flex items-center text-sm text-muted-foreground">
              <span>Made with</span>
              <Heart className="w-4 h-4 mx-1 text-red-500" />
              <span>for Grow A Garden players</span>
            </div>
          </div>

          {/* Main Features */}
          <div>
            <h3 className="font-semibold mb-3">Main Features</h3>
            <ul className="space-y-2">
              {links.main.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Game Resources */}
          <div>
            <h3 className="font-semibold mb-3">Game Resources</h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="https://www.roblox.com/games/grow-a-garden"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1"
                >
                  Play Grow A Garden
                  <ExternalLink className="w-3 h-3" />
                </a>
              </li>
            </ul>
          </div>
        </div>

        <Separator className="my-6" />

        {/* Bottom Information */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <div className="text-sm text-muted-foreground text-center sm:text-left">
            <p>© {currentYear} Grow A Garden Calculator. All rights reserved.</p>
            <p className="mt-1">
              This website is not affiliated with Gamer Robot Inc or the Grow A Garden game.
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-xs text-muted-foreground">
              Free online calculator tool
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
