'use client';

import React from 'react';
import { Mutation } from '@/types';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';

interface MutationStatsProps {
  mutations: Mutation[];
  filteredMutations: Mutation[];
}

export function MutationStats({ mutations, filteredMutations }: MutationStatsProps) {
  // Calculate statistics
  const stats = React.useMemo(() => {
    const totalMutations = mutations.length;
    const filteredCount = filteredMutations.length;

    // Count by type
    const typeCounts = filteredMutations.reduce((acc, mutation) => {
      acc[mutation.type] = (acc[mutation.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Multiplier statistics
    const multipliers = filteredMutations.map(m => m.multiplier);
    const minMultiplier = Math.min(...multipliers);
    const maxMultiplier = Math.max(...multipliers);
    const avgMultiplier = multipliers.reduce((sum, mult) => sum + mult, 0) / multipliers.length;

    // Group by multiplier ranges
    const multiplierRanges = {
      low: filteredMutations.filter(m => m.multiplier <= 5).length,
      medium: filteredMutations.filter(m => m.multiplier > 5 && m.multiplier <= 20).length,
      high: filteredMutations.filter(m => m.multiplier > 20 && m.multiplier < 100).length,
      ultra: filteredMutations.filter(m => m.multiplier >= 100).length
    };

    // Stack bonus statistics
    const stackableMutations = filteredMutations.filter(m => m.stackBonus > 0).length;

    return {
      totalMutations,
      filteredCount,
      typeCounts,
      multiplierStats: { min: minMultiplier, max: maxMultiplier, avg: avgMultiplier },
      multiplierRanges,
      stackableMutations
    };
  }, [mutations, filteredMutations]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Mutation Statistics</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Statistics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{stats.filteredCount}</div>
            <div className="text-sm text-muted-foreground">Showing</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-muted-foreground">{stats.totalMutations}</div>
            <div className="text-sm text-muted-foreground">Total Mutations</div>
          </div>
        </div>

        {/* Type Distribution */}
        <div>
          <h4 className="font-semibold mb-2">Type Distribution</h4>
          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span>⭐ Growth Mutations</span>
              <span className="font-medium">{stats.typeCounts.Growth || 0}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>❄️ Temperature Mutations</span>
              <span className="font-medium">{stats.typeCounts.Temperature || 0}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>✨ Environmental Mutations</span>
              <span className="font-medium">{stats.typeCounts.Environmental || 0}</span>
            </div>
          </div>
        </div>

        {/* Multiplier Statistics */}
        {stats.filteredCount > 0 && (
          <div>
            <h4 className="font-semibold mb-2">Multiplier Stats</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Minimum Multiplier</span>
                <span className="font-medium">×{stats.multiplierStats.min}</span>
              </div>
              <div className="flex justify-between">
                <span>Maximum Multiplier</span>
                <span className="font-medium">×{stats.multiplierStats.max}</span>
              </div>
              <div className="flex justify-between">
                <span>Average Multiplier</span>
                <span className="font-medium">×{Math.round(stats.multiplierStats.avg)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Multiplier Range Distribution */}
        <div>
          <h4 className="font-semibold mb-2">Multiplier Range Distribution</h4>
          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span>Low Tier (1-5x)</span>
              <span className="font-medium">{stats.multiplierRanges.low}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Mid Tier (6-20x)</span>
              <span className="font-medium">{stats.multiplierRanges.medium}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>High Tier (21-99x)</span>
              <span className="font-medium">{stats.multiplierRanges.high}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Ultra Tier (100x+)</span>
              <span className="font-medium">{stats.multiplierRanges.ultra}</span>
            </div>
          </div>
        </div>

        {/* Other Statistics */}
        <div>
          <h4 className="font-semibold mb-2">Other Information</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Stackable Mutations</span>
              <span className="font-medium">{stats.stackableMutations}</span>
            </div>
            <div className="flex justify-between">
              <span>Standalone Mutations</span>
              <span className="font-medium">{stats.filteredCount - stats.stackableMutations}</span>
            </div>
          </div>
        </div>

        {/* Mutation Rules */}
        <div className="pt-2 border-t">
          <h4 className="font-semibold mb-2 text-sm">Mutation Rules</h4>
          <div className="text-xs text-muted-foreground space-y-1">
            <p>• Only one Growth mutation can be selected</p>
            <p>• Only one Temperature mutation can be selected</p>
            <p>• Environmental mutations can stack</p>
            <p>• Some mutations have special constraints</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
