'use client';

import React from 'react';
import { CropTier, HarvestType } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface CropFiltersProps {
  searchQuery: string;
  selectedTier: string;
  selectedHarvestType: string;
  showObtainableOnly: boolean;
  onSearchChange: (query: string) => void;
  onTierChange: (tier: string) => void;
  onHarvestTypeChange: (type: string) => void;
  onObtainableToggle: (obtainable: boolean) => void;
  onReset: () => void;
}

export function CropFilters({
  searchQuery,
  selectedTier,
  selectedHarvestType,
  showObtainableOnly,
  onSearchChange,
  onTierChange,
  onHarvestTypeChange,
  onObtainableToggle,
  onReset
}: CropFiltersProps) {
  const tiers: CropTier[] = ['Common', 'Uncommon', 'Rare', 'Legendary', 'Mythical', 'Divine', 'Prismatic', 'Transcendent'];
  const harvestTypes: HarvestType[] = ['Single', 'Multi'];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle>Filter Crops</CardTitle>
        <Button variant="outline" size="sm" onClick={onReset}>
          Reset Filters
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search Box */}
        <div>
          <Input
            placeholder="Search crop names..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>

        {/* Filter Options */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Tier Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Tier</label>
            <Select value={selectedTier} onValueChange={onTierChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select tier" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tiers</SelectItem>
                {tiers.map(tier => (
                  <SelectItem key={tier} value={tier}>{tier}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Harvest Type Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Harvest Type</label>
            <Select value={selectedHarvestType} onValueChange={onHarvestTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select harvest type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Single">Single Harvest</SelectItem>
                <SelectItem value="Multi">Multi Harvest</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Obtainability Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Obtainability</label>
            <Select
              value={showObtainableOnly ? "obtainable" : "all"}
              onValueChange={(value) => onObtainableToggle(value === "obtainable")}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select obtainability" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Crops</SelectItem>
                <SelectItem value="obtainable">Obtainable Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Quick Filter Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedTier === 'Prismatic' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onTierChange('Prismatic')}
          >
            Prismatic
          </Button>
          <Button
            variant={selectedTier === 'Divine' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onTierChange('Divine')}
          >
            Divine
          </Button>
          <Button
            variant={selectedTier === 'Mythical' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onTierChange('Mythical')}
          >
            Mythical
          </Button>
          <Button
            variant={selectedHarvestType === 'Multi' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onHarvestTypeChange('Multi')}
          >
            Multi Harvest
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
