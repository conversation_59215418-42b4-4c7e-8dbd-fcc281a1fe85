/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5Cgrowagardencalculator%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5Cgrowagardencalculator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5Cgrowagardencalculator%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5Cgrowagardencalculator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5Cgrowagardencalculator%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5Cgrowagardencalculator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cmain-layout.tsx%22%2C%22ids%22%3A%5B%22MainLayout%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cmain-layout.tsx%22%2C%22ids%22%3A%5B%22MainLayout%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/main-layout.tsx */ \"(ssr)/./src/components/layout/main-layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXb3JrU3BhY2UlNUMlNUNncm93YWdhcmRlbmNhbGN1bGF0b3IlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1dvcmtTcGFjZSU1QyU1Q2dyb3dhZ2FyZGVuY2FsY3VsYXRvciU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXb3JrU3BhY2UlNUMlNUNncm93YWdhcmRlbmNhbGN1bGF0b3IlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0JTVDJTVDbWFpbi1sYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTWFpbkxheW91dCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMExBQW1KIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3Jvdy1hLWdhcmRlbi1jYWxjdWxhdG9yLz9hOTJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiTWFpbkxheW91dFwiXSAqLyBcIkM6XFxcXFdvcmtTcGFjZVxcXFxncm93YWdhcmRlbmNhbGN1bGF0b3JcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0XFxcXG1haW4tbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cmain-layout.tsx%22%2C%22ids%22%3A%5B%22MainLayout%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNXb3JrU3BhY2UlNUMlNUNncm93YWdhcmRlbmNhbGN1bGF0b3IlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTZGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3Jvdy1hLWdhcmRlbi1jYWxjdWxhdG9yLz9mZTdhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcV29ya1NwYWNlXFxcXGdyb3dhZ2FyZGVuY2FsY3VsYXRvclxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CWorkSpace%5C%5Cgrowagardencalculator%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_calculator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/calculator */ \"(ssr)/./src/lib/calculator.ts\");\n/* harmony import */ var _components_calculator_crop_selector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/calculator/crop-selector */ \"(ssr)/./src/components/calculator/crop-selector.tsx\");\n/* harmony import */ var _components_calculator_mutation_selector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/calculator/mutation-selector */ \"(ssr)/./src/components/calculator/mutation-selector.tsx\");\n/* harmony import */ var _components_calculator_calculation_inputs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/calculator/calculation-inputs */ \"(ssr)/./src/components/calculator/calculation-inputs.tsx\");\n/* harmony import */ var _components_calculator_calculation_result__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/calculator/calculation-result */ \"(ssr)/./src/components/calculator/calculation-result.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction HomePage() {\n    const [selectedCrop, setSelectedCrop] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(null);\n    const [selectedMutations, setSelectedMutations] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const [weight, setWeight] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(1);\n    const [quantity, setQuantity] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(1);\n    const [friendBoost, setFriendBoost] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0);\n    const [result, setResult] = react__WEBPACK_IMPORTED_MODULE_1___default().useState({\n        totalValue: 0,\n        totalMultiplier: 0,\n        baseValue: 0,\n        mutationMultiplier: 0,\n        details: {\n            cropName: \"\",\n            weight: 0,\n            quantity: 0,\n            friendBoost: 0,\n            mutations: []\n        }\n    });\n    // Structured data for SEO\n    const structuredData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"WebApplication\",\n        \"name\": \"Grow A Garden Calculator\",\n        \"description\": \"The ultimate crop value calculator for Roblox Grow A Garden with mutations, weights, and friend boosts\",\n        \"url\": \"https://growagardencalculator.com\",\n        \"applicationCategory\": \"GameApplication\",\n        \"operatingSystem\": \"Web Browser\",\n        \"offers\": {\n            \"@type\": \"Offer\",\n            \"price\": \"0\",\n            \"priceCurrency\": \"USD\"\n        },\n        \"featureList\": [\n            \"Crop value calculation\",\n            \"Mutation system\",\n            \"100+ crops database\",\n            \"40+ mutations database\",\n            \"Friend boost calculation\"\n        ]\n    };\n    // Recalculate when inputs change\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (selectedCrop) {\n            const input = {\n                crop: selectedCrop,\n                weight,\n                quantity,\n                friendBoost,\n                selectedMutations\n            };\n            try {\n                const errors = (0,_lib_calculator__WEBPACK_IMPORTED_MODULE_2__.validateCalculatorInput)(input);\n                if (errors.length === 0) {\n                    const newResult = (0,_lib_calculator__WEBPACK_IMPORTED_MODULE_2__.calculateCropValue)(input);\n                    setResult(newResult);\n                }\n            } catch (error) {\n                console.error(\"Calculation error:\", error);\n            }\n        }\n    }, [\n        selectedCrop,\n        weight,\n        quantity,\n        friendBoost,\n        selectedMutations\n    ]);\n    // When selecting a new crop, set recommended weight\n    const handleCropSelect = (crop)=>{\n        setSelectedCrop(crop);\n        setWeight(crop.minimumWeight * 1.5); // Set to recommended weight\n    };\n    // Reset all inputs\n    const handleReset = ()=>{\n        setSelectedCrop(null);\n        setSelectedMutations([]);\n        setWeight(1);\n        setQuantity(1);\n        setFriendBoost(0);\n        setResult({\n            totalValue: 0,\n            totalMultiplier: 0,\n            baseValue: 0,\n            mutationMultiplier: 0,\n            details: {\n                cropName: \"\",\n                weight: 0,\n                quantity: 0,\n                friendBoost: 0,\n                mutations: []\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(structuredData)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold mb-2\",\n                        children: \"Grow A Garden Calculator\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-muted-foreground mb-4\",\n                        children: \"The ultimate crop value calculator for Roblox Grow A Garden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-3xl mx-auto text-sm text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Calculate precise crop values with mutations, weights, and friend boosts. Features complete database of 100+ crops and 40+ mutations. Free online tool for Roblox Grow A Garden players.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6 max-w-4xl mx-auto bg-background/80\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_calculator_crop_selector__WEBPACK_IMPORTED_MODULE_3__.CropSelector, {\n                            selectedCrop: selectedCrop,\n                            onCropSelect: handleCropSelect\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_calculator_mutation_selector__WEBPACK_IMPORTED_MODULE_4__.MutationSelector, {\n                            selectedMutations: selectedMutations,\n                            onMutationsChange: setSelectedMutations\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_calculator_calculation_inputs__WEBPACK_IMPORTED_MODULE_5__.CalculationInputs, {\n                            crop: selectedCrop,\n                            weight: weight,\n                            quantity: quantity,\n                            friendBoost: friendBoost,\n                            onWeightChange: setWeight,\n                            onQuantityChange: setQuantity,\n                            onFriendBoostChange: setFriendBoost,\n                            onReset: handleReset\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_calculator_calculation_result__WEBPACK_IMPORTED_MODULE_6__.CalculationResult, {\n                            result: result\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRTBCO0FBRXFEO0FBQ1Y7QUFDUTtBQUNFO0FBQzhCO0FBRTlGLFNBQVNRO0lBQ3RCLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUdWLHFEQUFjLENBQWM7SUFDcEUsTUFBTSxDQUFDWSxtQkFBbUJDLHFCQUFxQixHQUFHYixxREFBYyxDQUFhLEVBQUU7SUFDL0UsTUFBTSxDQUFDYyxRQUFRQyxVQUFVLEdBQUdmLHFEQUFjLENBQVM7SUFDbkQsTUFBTSxDQUFDZ0IsVUFBVUMsWUFBWSxHQUFHakIscURBQWMsQ0FBUztJQUN2RCxNQUFNLENBQUNrQixhQUFhQyxlQUFlLEdBQUduQixxREFBYyxDQUFTO0lBQzdELE1BQU0sQ0FBQ29CLFFBQVFDLFVBQVUsR0FBR3JCLHFEQUFjLENBQW9CO1FBQzVEc0IsWUFBWTtRQUNaQyxpQkFBaUI7UUFDakJDLFdBQVc7UUFDWEMsb0JBQW9CO1FBQ3BCQyxTQUFTO1lBQ1BDLFVBQVU7WUFDVmIsUUFBUTtZQUNSRSxVQUFVO1lBQ1ZFLGFBQWE7WUFDYlUsV0FBVyxFQUFFO1FBQ2Y7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNQyxpQkFBaUI7UUFDckIsWUFBWTtRQUNaLFNBQVM7UUFDVCxRQUFRO1FBQ1IsZUFBZTtRQUNmLE9BQU87UUFDUCx1QkFBdUI7UUFDdkIsbUJBQW1CO1FBQ25CLFVBQVU7WUFDUixTQUFTO1lBQ1QsU0FBUztZQUNULGlCQUFpQjtRQUNuQjtRQUNBLGVBQWU7WUFDYjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUVBLGlDQUFpQztJQUNqQzdCLHNEQUFlLENBQUM7UUFDZCxJQUFJUyxjQUFjO1lBQ2hCLE1BQU1zQixRQUF5QjtnQkFDN0JDLE1BQU12QjtnQkFDTks7Z0JBQ0FFO2dCQUNBRTtnQkFDQU47WUFDRjtZQUVBLElBQUk7Z0JBQ0YsTUFBTXFCLFNBQVMvQix3RUFBdUJBLENBQUM2QjtnQkFDdkMsSUFBSUUsT0FBT0MsTUFBTSxLQUFLLEdBQUc7b0JBQ3ZCLE1BQU1DLFlBQVlsQyxtRUFBa0JBLENBQUM4QjtvQkFDckNWLFVBQVVjO2dCQUNaO1lBQ0YsRUFBRSxPQUFPQyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsc0JBQXNCQTtZQUN0QztRQUNGO0lBQ0YsR0FBRztRQUFDM0I7UUFBY0s7UUFBUUU7UUFBVUU7UUFBYU47S0FBa0I7SUFFbkUsb0RBQW9EO0lBQ3BELE1BQU0wQixtQkFBbUIsQ0FBQ047UUFDeEJ0QixnQkFBZ0JzQjtRQUNoQmpCLFVBQVVpQixLQUFLTyxhQUFhLEdBQUcsTUFBTSw0QkFBNEI7SUFDbkU7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTUMsY0FBYztRQUNsQjlCLGdCQUFnQjtRQUNoQkcscUJBQXFCLEVBQUU7UUFDdkJFLFVBQVU7UUFDVkUsWUFBWTtRQUNaRSxlQUFlO1FBQ2ZFLFVBQVU7WUFDUkMsWUFBWTtZQUNaQyxpQkFBaUI7WUFDakJDLFdBQVc7WUFDWEMsb0JBQW9CO1lBQ3BCQyxTQUFTO2dCQUNQQyxVQUFVO2dCQUNWYixRQUFRO2dCQUNSRSxVQUFVO2dCQUNWRSxhQUFhO2dCQUNiVSxXQUFXLEVBQUU7WUFDZjtRQUNGO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ2E7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUNDQyxNQUFLO2dCQUNMQyx5QkFBeUI7b0JBQUVDLFFBQVFDLEtBQUtDLFNBQVMsQ0FBQ25CO2dCQUFnQjs7Ozs7OzBCQUdwRSw4REFBQ1k7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTzt3QkFBR1AsV0FBVTtrQ0FBMEI7Ozs7OztrQ0FDeEMsOERBQUNRO3dCQUFFUixXQUFVO2tDQUFxQzs7Ozs7O2tDQUdsRCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNRO3NDQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFTUCw4REFBQ1Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ3ZDLDhFQUFZQTs0QkFDWE0sY0FBY0E7NEJBQ2QwQyxjQUFjYjs7Ozs7Ozs7Ozs7a0NBS2xCLDhEQUFDRzt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ3RDLHNGQUFnQkE7NEJBQ2ZRLG1CQUFtQkE7NEJBQ25Cd0MsbUJBQW1CdkM7Ozs7Ozs7Ozs7O2tDQUt2Qiw4REFBQzRCO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDckMsd0ZBQWlCQTs0QkFDaEIyQixNQUFNdkI7NEJBQ05LLFFBQVFBOzRCQUNSRSxVQUFVQTs0QkFDVkUsYUFBYUE7NEJBQ2JtQyxnQkFBZ0J0Qzs0QkFDaEJ1QyxrQkFBa0JyQzs0QkFDbEJzQyxxQkFBcUJwQzs0QkFDckJxQyxTQUFTaEI7Ozs7Ozs7Ozs7O2tDQUtiLDhEQUFDQzt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ25DLHdGQUEwQkE7NEJBQUNhLFFBQVFBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU85QyIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb3ctYS1nYXJkZW4tY2FsY3VsYXRvci8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBDcm9wLCBNdXRhdGlvbiwgQ2FsY3VsYXRvcklucHV0LCBDYWxjdWxhdGlvblJlc3VsdCB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgY2FsY3VsYXRlQ3JvcFZhbHVlLCB2YWxpZGF0ZUNhbGN1bGF0b3JJbnB1dCB9IGZyb20gJ0AvbGliL2NhbGN1bGF0b3InO1xuaW1wb3J0IHsgQ3JvcFNlbGVjdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL2NhbGN1bGF0b3IvY3JvcC1zZWxlY3Rvcic7XG5pbXBvcnQgeyBNdXRhdGlvblNlbGVjdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL2NhbGN1bGF0b3IvbXV0YXRpb24tc2VsZWN0b3InO1xuaW1wb3J0IHsgQ2FsY3VsYXRpb25JbnB1dHMgfSBmcm9tICdAL2NvbXBvbmVudHMvY2FsY3VsYXRvci9jYWxjdWxhdGlvbi1pbnB1dHMnO1xuaW1wb3J0IHsgQ2FsY3VsYXRpb25SZXN1bHQgYXMgQ2FsY3VsYXRpb25SZXN1bHRDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvY2FsY3VsYXRvci9jYWxjdWxhdGlvbi1yZXN1bHQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lUGFnZSgpIHtcbiAgY29uc3QgW3NlbGVjdGVkQ3JvcCwgc2V0U2VsZWN0ZWRDcm9wXSA9IFJlYWN0LnVzZVN0YXRlPENyb3AgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkTXV0YXRpb25zLCBzZXRTZWxlY3RlZE11dGF0aW9uc10gPSBSZWFjdC51c2VTdGF0ZTxNdXRhdGlvbltdPihbXSk7XG4gIGNvbnN0IFt3ZWlnaHQsIHNldFdlaWdodF0gPSBSZWFjdC51c2VTdGF0ZTxudW1iZXI+KDEpO1xuICBjb25zdCBbcXVhbnRpdHksIHNldFF1YW50aXR5XSA9IFJlYWN0LnVzZVN0YXRlPG51bWJlcj4oMSk7XG4gIGNvbnN0IFtmcmllbmRCb29zdCwgc2V0RnJpZW5kQm9vc3RdID0gUmVhY3QudXNlU3RhdGU8bnVtYmVyPigwKTtcbiAgY29uc3QgW3Jlc3VsdCwgc2V0UmVzdWx0XSA9IFJlYWN0LnVzZVN0YXRlPENhbGN1bGF0aW9uUmVzdWx0Pih7XG4gICAgdG90YWxWYWx1ZTogMCxcbiAgICB0b3RhbE11bHRpcGxpZXI6IDAsXG4gICAgYmFzZVZhbHVlOiAwLFxuICAgIG11dGF0aW9uTXVsdGlwbGllcjogMCxcbiAgICBkZXRhaWxzOiB7XG4gICAgICBjcm9wTmFtZTogJycsXG4gICAgICB3ZWlnaHQ6IDAsXG4gICAgICBxdWFudGl0eTogMCxcbiAgICAgIGZyaWVuZEJvb3N0OiAwLFxuICAgICAgbXV0YXRpb25zOiBbXVxuICAgIH1cbiAgfSk7XG5cbiAgLy8gU3RydWN0dXJlZCBkYXRhIGZvciBTRU9cbiAgY29uc3Qgc3RydWN0dXJlZERhdGEgPSB7XG4gICAgXCJAY29udGV4dFwiOiBcImh0dHBzOi8vc2NoZW1hLm9yZ1wiLFxuICAgIFwiQHR5cGVcIjogXCJXZWJBcHBsaWNhdGlvblwiLFxuICAgIFwibmFtZVwiOiBcIkdyb3cgQSBHYXJkZW4gQ2FsY3VsYXRvclwiLFxuICAgIFwiZGVzY3JpcHRpb25cIjogXCJUaGUgdWx0aW1hdGUgY3JvcCB2YWx1ZSBjYWxjdWxhdG9yIGZvciBSb2Jsb3ggR3JvdyBBIEdhcmRlbiB3aXRoIG11dGF0aW9ucywgd2VpZ2h0cywgYW5kIGZyaWVuZCBib29zdHNcIixcbiAgICBcInVybFwiOiBcImh0dHBzOi8vZ3Jvd2FnYXJkZW5jYWxjdWxhdG9yLmNvbVwiLFxuICAgIFwiYXBwbGljYXRpb25DYXRlZ29yeVwiOiBcIkdhbWVBcHBsaWNhdGlvblwiLFxuICAgIFwib3BlcmF0aW5nU3lzdGVtXCI6IFwiV2ViIEJyb3dzZXJcIixcbiAgICBcIm9mZmVyc1wiOiB7XG4gICAgICBcIkB0eXBlXCI6IFwiT2ZmZXJcIixcbiAgICAgIFwicHJpY2VcIjogXCIwXCIsXG4gICAgICBcInByaWNlQ3VycmVuY3lcIjogXCJVU0RcIlxuICAgIH0sXG4gICAgXCJmZWF0dXJlTGlzdFwiOiBbXG4gICAgICBcIkNyb3AgdmFsdWUgY2FsY3VsYXRpb25cIixcbiAgICAgIFwiTXV0YXRpb24gc3lzdGVtXCIsXG4gICAgICBcIjEwMCsgY3JvcHMgZGF0YWJhc2VcIixcbiAgICAgIFwiNDArIG11dGF0aW9ucyBkYXRhYmFzZVwiLFxuICAgICAgXCJGcmllbmQgYm9vc3QgY2FsY3VsYXRpb25cIlxuICAgIF1cbiAgfTtcblxuICAvLyBSZWNhbGN1bGF0ZSB3aGVuIGlucHV0cyBjaGFuZ2VcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRDcm9wKSB7XG4gICAgICBjb25zdCBpbnB1dDogQ2FsY3VsYXRvcklucHV0ID0ge1xuICAgICAgICBjcm9wOiBzZWxlY3RlZENyb3AsXG4gICAgICAgIHdlaWdodCxcbiAgICAgICAgcXVhbnRpdHksXG4gICAgICAgIGZyaWVuZEJvb3N0LFxuICAgICAgICBzZWxlY3RlZE11dGF0aW9uc1xuICAgICAgfTtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgZXJyb3JzID0gdmFsaWRhdGVDYWxjdWxhdG9ySW5wdXQoaW5wdXQpO1xuICAgICAgICBpZiAoZXJyb3JzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgIGNvbnN0IG5ld1Jlc3VsdCA9IGNhbGN1bGF0ZUNyb3BWYWx1ZShpbnB1dCk7XG4gICAgICAgICAgc2V0UmVzdWx0KG5ld1Jlc3VsdCk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0NhbGN1bGF0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtzZWxlY3RlZENyb3AsIHdlaWdodCwgcXVhbnRpdHksIGZyaWVuZEJvb3N0LCBzZWxlY3RlZE11dGF0aW9uc10pO1xuXG4gIC8vIFdoZW4gc2VsZWN0aW5nIGEgbmV3IGNyb3AsIHNldCByZWNvbW1lbmRlZCB3ZWlnaHRcbiAgY29uc3QgaGFuZGxlQ3JvcFNlbGVjdCA9IChjcm9wOiBDcm9wKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRDcm9wKGNyb3ApO1xuICAgIHNldFdlaWdodChjcm9wLm1pbmltdW1XZWlnaHQgKiAxLjUpOyAvLyBTZXQgdG8gcmVjb21tZW5kZWQgd2VpZ2h0XG4gIH07XG5cbiAgLy8gUmVzZXQgYWxsIGlucHV0c1xuICBjb25zdCBoYW5kbGVSZXNldCA9ICgpID0+IHtcbiAgICBzZXRTZWxlY3RlZENyb3AobnVsbCk7XG4gICAgc2V0U2VsZWN0ZWRNdXRhdGlvbnMoW10pO1xuICAgIHNldFdlaWdodCgxKTtcbiAgICBzZXRRdWFudGl0eSgxKTtcbiAgICBzZXRGcmllbmRCb29zdCgwKTtcbiAgICBzZXRSZXN1bHQoe1xuICAgICAgdG90YWxWYWx1ZTogMCxcbiAgICAgIHRvdGFsTXVsdGlwbGllcjogMCxcbiAgICAgIGJhc2VWYWx1ZTogMCxcbiAgICAgIG11dGF0aW9uTXVsdGlwbGllcjogMCxcbiAgICAgIGRldGFpbHM6IHtcbiAgICAgICAgY3JvcE5hbWU6ICcnLFxuICAgICAgICB3ZWlnaHQ6IDAsXG4gICAgICAgIHF1YW50aXR5OiAwLFxuICAgICAgICBmcmllbmRCb29zdDogMCxcbiAgICAgICAgbXV0YXRpb25zOiBbXVxuICAgICAgfVxuICAgIH0pO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LThcIj5cbiAgICAgIHsvKiBTdHJ1Y3R1cmVkIGRhdGEgZm9yIFNFTyAqL31cbiAgICAgIDxzY3JpcHRcbiAgICAgICAgdHlwZT1cImFwcGxpY2F0aW9uL2xkK2pzb25cIlxuICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17eyBfX2h0bWw6IEpTT04uc3RyaW5naWZ5KHN0cnVjdHVyZWREYXRhKSB9fVxuICAgICAgLz5cbiAgICAgIHsvKiBQYWdlIFRpdGxlICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi04XCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgbWItMlwiPkdyb3cgQSBHYXJkZW4gQ2FsY3VsYXRvcjwvaDE+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTRcIj5cbiAgICAgICAgICBUaGUgdWx0aW1hdGUgY3JvcCB2YWx1ZSBjYWxjdWxhdG9yIGZvciBSb2Jsb3ggR3JvdyBBIEdhcmRlblxuICAgICAgICA8L3A+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctM3hsIG14LWF1dG8gdGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICA8cD5cbiAgICAgICAgICAgIENhbGN1bGF0ZSBwcmVjaXNlIGNyb3AgdmFsdWVzIHdpdGggbXV0YXRpb25zLCB3ZWlnaHRzLCBhbmQgZnJpZW5kIGJvb3N0cy5cbiAgICAgICAgICAgIEZlYXR1cmVzIGNvbXBsZXRlIGRhdGFiYXNlIG9mIDEwMCsgY3JvcHMgYW5kIDQwKyBtdXRhdGlvbnMuXG4gICAgICAgICAgICBGcmVlIG9ubGluZSB0b29sIGZvciBSb2Jsb3ggR3JvdyBBIEdhcmRlbiBwbGF5ZXJzLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTYgbWF4LXctNHhsIG14LWF1dG8gYmctYmFja2dyb3VuZC84MFwiPlxuICAgICAgICB7LyogQ3JvcCBTZWxlY3RvciBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgIDxDcm9wU2VsZWN0b3JcbiAgICAgICAgICAgIHNlbGVjdGVkQ3JvcD17c2VsZWN0ZWRDcm9wfVxuICAgICAgICAgICAgb25Dcm9wU2VsZWN0PXtoYW5kbGVDcm9wU2VsZWN0fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNdXRhdGlvbiBTZWxlY3RvciBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgIDxNdXRhdGlvblNlbGVjdG9yXG4gICAgICAgICAgICBzZWxlY3RlZE11dGF0aW9ucz17c2VsZWN0ZWRNdXRhdGlvbnN9XG4gICAgICAgICAgICBvbk11dGF0aW9uc0NoYW5nZT17c2V0U2VsZWN0ZWRNdXRhdGlvbnN9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIENhbGN1bGF0aW9uIElucHV0cyBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgIDxDYWxjdWxhdGlvbklucHV0c1xuICAgICAgICAgICAgY3JvcD17c2VsZWN0ZWRDcm9wfVxuICAgICAgICAgICAgd2VpZ2h0PXt3ZWlnaHR9XG4gICAgICAgICAgICBxdWFudGl0eT17cXVhbnRpdHl9XG4gICAgICAgICAgICBmcmllbmRCb29zdD17ZnJpZW5kQm9vc3R9XG4gICAgICAgICAgICBvbldlaWdodENoYW5nZT17c2V0V2VpZ2h0fVxuICAgICAgICAgICAgb25RdWFudGl0eUNoYW5nZT17c2V0UXVhbnRpdHl9XG4gICAgICAgICAgICBvbkZyaWVuZEJvb3N0Q2hhbmdlPXtzZXRGcmllbmRCb29zdH1cbiAgICAgICAgICAgIG9uUmVzZXQ9e2hhbmRsZVJlc2V0fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSZXN1bHRzIFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICAgICAgPENhbGN1bGF0aW9uUmVzdWx0Q29tcG9uZW50IHJlc3VsdD17cmVzdWx0fSAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNhbGN1bGF0ZUNyb3BWYWx1ZSIsInZhbGlkYXRlQ2FsY3VsYXRvcklucHV0IiwiQ3JvcFNlbGVjdG9yIiwiTXV0YXRpb25TZWxlY3RvciIsIkNhbGN1bGF0aW9uSW5wdXRzIiwiQ2FsY3VsYXRpb25SZXN1bHQiLCJDYWxjdWxhdGlvblJlc3VsdENvbXBvbmVudCIsIkhvbWVQYWdlIiwic2VsZWN0ZWRDcm9wIiwic2V0U2VsZWN0ZWRDcm9wIiwidXNlU3RhdGUiLCJzZWxlY3RlZE11dGF0aW9ucyIsInNldFNlbGVjdGVkTXV0YXRpb25zIiwid2VpZ2h0Iiwic2V0V2VpZ2h0IiwicXVhbnRpdHkiLCJzZXRRdWFudGl0eSIsImZyaWVuZEJvb3N0Iiwic2V0RnJpZW5kQm9vc3QiLCJyZXN1bHQiLCJzZXRSZXN1bHQiLCJ0b3RhbFZhbHVlIiwidG90YWxNdWx0aXBsaWVyIiwiYmFzZVZhbHVlIiwibXV0YXRpb25NdWx0aXBsaWVyIiwiZGV0YWlscyIsImNyb3BOYW1lIiwibXV0YXRpb25zIiwic3RydWN0dXJlZERhdGEiLCJ1c2VFZmZlY3QiLCJpbnB1dCIsImNyb3AiLCJlcnJvcnMiLCJsZW5ndGgiLCJuZXdSZXN1bHQiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVDcm9wU2VsZWN0IiwibWluaW11bVdlaWdodCIsImhhbmRsZVJlc2V0IiwiZGl2IiwiY2xhc3NOYW1lIiwic2NyaXB0IiwidHlwZSIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiSlNPTiIsInN0cmluZ2lmeSIsImgxIiwicCIsIm9uQ3JvcFNlbGVjdCIsIm9uTXV0YXRpb25zQ2hhbmdlIiwib25XZWlnaHRDaGFuZ2UiLCJvblF1YW50aXR5Q2hhbmdlIiwib25GcmllbmRCb29zdENoYW5nZSIsIm9uUmVzZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/calculator/calculation-inputs.tsx":
/*!**********************************************************!*\
  !*** ./src/components/calculator/calculation-inputs.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalculationInputs: () => (/* binding */ CalculationInputs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_calculator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/calculator */ \"(ssr)/./src/lib/calculator.ts\");\n/* __next_internal_client_entry_do_not_use__ CalculationInputs auto */ \n\n\n\n\n\nfunction CalculationInputs({ crop, weight, quantity, friendBoost, onWeightChange, onQuantityChange, onFriendBoostChange, onReset }) {\n    const weightImpact = crop ? (0,_lib_calculator__WEBPACK_IMPORTED_MODULE_5__.calculateWeightImpact)(crop, weight) : null;\n    const recommendedWeight = crop ? (0,_lib_calculator__WEBPACK_IMPORTED_MODULE_5__.getRecommendedWeight)(crop) : 0;\n    const handleWeightChange = (e)=>{\n        const value = parseFloat(e.target.value) || 0;\n        onWeightChange(Math.max(0, value));\n    };\n    const handleQuantityChange = (e)=>{\n        const value = parseInt(e.target.value) || 0;\n        onQuantityChange(Math.max(0, value));\n    };\n    const handleFriendBoostChange = (e)=>{\n        const value = parseFloat(e.target.value) || 0;\n        onFriendBoostChange(Math.max(0, Math.min(1000, value)));\n    };\n    const setRecommendedWeight = ()=>{\n        if (crop) {\n            onWeightChange(recommendedWeight);\n        }\n    };\n    const setMinimumWeight = ()=>{\n        if (crop) {\n            onWeightChange(crop.minimumWeight);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"bg-card/50 border-border/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Weight (kg)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    type: \"number\",\n                                    value: weight,\n                                    onChange: handleWeightChange,\n                                    placeholder: \"2.85\",\n                                    step: \"0.01\",\n                                    min: \"0\",\n                                    className: \"bg-background/50 border-border/50 text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Quantity (pcs)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    type: \"number\",\n                                    value: quantity,\n                                    onChange: handleQuantityChange,\n                                    placeholder: \"1\",\n                                    min: \"1\",\n                                    className: \"bg-background/50 border-border/50 text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Friend Boost\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"range\",\n                                            min: \"0\",\n                                            max: \"1000\",\n                                            step: \"1\",\n                                            value: friendBoost,\n                                            onChange: handleFriendBoostChange,\n                                            className: \"flex-1 h-2 bg-background/50 rounded-lg appearance-none cursor-pointer\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium w-12\",\n                                            children: [\n                                                friendBoost,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Max Mutation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: onReset,\n                                    className: \"w-full bg-background/50 border-border/50 text-sm\",\n                                    children: \"Reset\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                crop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pt-2 border-t space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-semibold\",\n                            children: \"Crop Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-2 text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Tier:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 font-medium\",\n                                            children: crop.tier\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Harvest Type:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 font-medium\",\n                                            children: crop.harvestType\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Min Value:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 font-medium\",\n                                            children: [\n                                                \"\\xa2\",\n                                                crop.minimumValue.toLocaleString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Seed Price:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 font-medium\",\n                                            children: [\n                                                \"\\xa2\",\n                                                crop.shecklePrice.toLocaleString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-inputs.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/calculator/calculation-inputs.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/calculator/calculation-result.tsx":
/*!**********************************************************!*\
  !*** ./src/components/calculator/calculation-result.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalculationResult: () => (/* binding */ CalculationResult)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_calculator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/calculator */ \"(ssr)/./src/lib/calculator.ts\");\n/* __next_internal_client_entry_do_not_use__ CalculationResult auto */ \n\n\n\nfunction CalculationResult({ result }) {\n    if (!result.details.cropName) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"bg-card/50 border-border/50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-muted-foreground\",\n                    children: \"Please select a crop and enter parameters to start calculating\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"bg-card/50 border-border/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: \"\\uD83D\\uDCB0 Total Value\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-4xl font-bold text-primary\",\n                        children: (0,_lib_calculator__WEBPACK_IMPORTED_MODULE_3__.formatSheckles)(result.totalValue)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Total Multiplier: \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"\\xd7\",\n                                        (0,_lib_calculator__WEBPACK_IMPORTED_MODULE_3__.formatNumber)(result.totalMultiplier)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Valuation Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 bg-primary rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: result.details.cropName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"weighing \",\n                                        result.details.weight,\n                                        \"kg, worth \",\n                                        (0,_lib_calculator__WEBPACK_IMPORTED_MODULE_3__.formatSheckles)(result.baseValue)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\calculation-result.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/calculator/calculation-result.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/calculator/crop-selector.tsx":
/*!*****************************************************!*\
  !*** ./src/components/calculator/crop-selector.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CropSelector: () => (/* binding */ CropSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _data_crops__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/crops */ \"(ssr)/./src/data/crops.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ CropSelector auto */ \n\n\n\n\n\nfunction CropSelector({ selectedCrop, onCropSelect }) {\n    const [searchQuery, setSearchQuery] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    const [selectedTier, setSelectedTier] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"all\");\n    // Filter crops\n    const filteredCrops = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        let filtered = _data_crops__WEBPACK_IMPORTED_MODULE_3__.crops;\n        // Filter by tier\n        if (selectedTier !== \"all\") {\n            filtered = filtered.filter((crop)=>crop.tier === selectedTier);\n        }\n        // Filter by search query\n        if (searchQuery) {\n            const query = searchQuery.toLowerCase();\n            filtered = filtered.filter((crop)=>crop.name.toLowerCase().includes(query));\n        }\n        return filtered;\n    }, [\n        searchQuery,\n        selectedTier\n    ]);\n    const tiers = [\n        \"Common\",\n        \"Uncommon\",\n        \"Rare\",\n        \"Legendary\",\n        \"Mythical\",\n        \"Divine\",\n        \"Prismatic\",\n        \"Transcendent\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"bg-card/50 border-border/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n            className: \"p-4 space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                        placeholder: \"Select Crop - enter text to search for crops\",\n                        value: searchQuery,\n                        onChange: (e)=>setSearchQuery(e.target.value),\n                        className: \"bg-background/50 border-border/50 text-sm\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-2 max-h-80 overflow-y-auto bg-background/30 p-3 rounded-lg border border-border/30\",\n                    children: filteredCrops.map((crop)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onCropSelect(crop),\n                            className: `\n                relative p-2 rounded-lg transition-all hover:scale-105 group\n                ${selectedCrop?.id === crop.id ? \"bg-primary/20 border-2 border-primary\" : \"bg-background/50 border border-border/50 hover:border-primary/50 hover:bg-background/80\"}\n              `,\n                            title: `${crop.name} (${crop.tier})`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: crop.image,\n                                    alt: crop.name,\n                                    width: 32,\n                                    height: 32,\n                                    className: \"w-8 h-8 mx-auto rounded\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs mt-1 truncate text-center\",\n                                    children: crop.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, this),\n                                selectedCrop?.id === crop.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, crop.id, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                filteredCrops.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-muted-foreground\",\n                    children: \"No matching crops found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\crop-selector.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/calculator/crop-selector.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/calculator/mutation-selector.tsx":
/*!*********************************************************!*\
  !*** ./src/components/calculator/mutation-selector.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationSelector: () => (/* binding */ MutationSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_mutations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/mutations */ \"(ssr)/./src/data/mutations.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ MutationSelector auto */ \n\n\n\nfunction MutationSelector({ selectedMutations, onMutationsChange }) {\n    const toggleMutation = (mutation)=>{\n        const isSelected = selectedMutations.some((m)=>m.id === mutation.id);\n        let newMutations;\n        if (isSelected) {\n            // Remove mutation\n            newMutations = selectedMutations.filter((m)=>m.id !== mutation.id);\n        } else {\n            // Add mutation\n            newMutations = [\n                ...selectedMutations,\n                mutation\n            ];\n        }\n        // Check compatibility\n        if ((0,_data_mutations__WEBPACK_IMPORTED_MODULE_2__.checkMutationCompatibility)(newMutations)) {\n            onMutationsChange(newMutations);\n        }\n    };\n    const clearAllMutations = ()=>{\n        onMutationsChange([]);\n    };\n    const renderMutationGroup = (title, mutations)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                    className: \"font-semibold text-sm flex items-center gap-2\",\n                    children: [\n                        title.includes(\"Growth\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-yellow-500\",\n                            children: \"⭐\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 38\n                        }, this),\n                        title.includes(\"Temperature\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-500\",\n                            children: \"❄️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 43\n                        }, this),\n                        title.includes(\"Environmental\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-500\",\n                            children: \"✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 45\n                        }, this),\n                        title.replace(/[⭐❄️✨]\\s*/, \"\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-background/30 p-3 rounded-lg border border-border/30\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1\",\n                        children: mutations.map((mutation)=>{\n                            const isSelected = selectedMutations.some((m)=>m.id === mutation.id);\n                            const testMutations = isSelected ? selectedMutations : [\n                                ...selectedMutations,\n                                mutation\n                            ];\n                            const isCompatible = (0,_data_mutations__WEBPACK_IMPORTED_MODULE_2__.checkMutationCompatibility)(testMutations);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>toggleMutation(mutation),\n                                disabled: !isSelected && !isCompatible,\n                                className: `\n                  px-3 py-1 rounded-full text-sm transition-all\n                  ${isSelected ? \"bg-primary/20 border border-primary text-primary\" : isCompatible ? \"bg-background/50 border border-border/50 hover:border-primary/50\" : \"bg-background/30 border border-border/30 opacity-50 cursor-not-allowed\"}\n                `,\n                                title: mutation.description,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: mutation.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-1 text-xs opacity-80\",\n                                        children: [\n                                            \"(\\xd7\",\n                                            mutation.multiplier,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, mutation.id, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            renderMutationGroup(\"⭐ Growth Mutations\", _data_mutations__WEBPACK_IMPORTED_MODULE_2__.growthMutations),\n            renderMutationGroup(\"❄️ Temperature Mutations\", _data_mutations__WEBPACK_IMPORTED_MODULE_2__.temperatureMutations),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-sm flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-500\",\n                                        children: \"✨\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Environmental Mutations\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    placeholder: \"Search mutations...\",\n                                    className: \"w-48 h-8 text-sm bg-background/50 border-border/50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-background/30 p-3 rounded-lg border border-border/30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-1\",\n                            children: _data_mutations__WEBPACK_IMPORTED_MODULE_2__.environmentalMutations.map((mutation)=>{\n                                const isSelected = selectedMutations.some((m)=>m.id === mutation.id);\n                                const testMutations = isSelected ? selectedMutations : [\n                                    ...selectedMutations,\n                                    mutation\n                                ];\n                                const isCompatible = (0,_data_mutations__WEBPACK_IMPORTED_MODULE_2__.checkMutationCompatibility)(testMutations);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleMutation(mutation),\n                                    disabled: !isSelected && !isCompatible,\n                                    className: `\n                    px-3 py-1 rounded-full text-sm transition-all\n                    ${isSelected ? \"bg-primary/20 border border-primary text-primary\" : isCompatible ? \"bg-background/50 border border-border/50 hover:border-primary/50\" : \"bg-background/30 border border-border/30 opacity-50 cursor-not-allowed\"}\n                  `,\n                                    title: mutation.description,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: mutation.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 text-xs opacity-80\",\n                                            children: [\n                                                \"(\\xd7\",\n                                                mutation.multiplier,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, mutation.id, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\calculator\\\\mutation-selector.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/calculator/mutation-selector.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/separator */ \"(ssr)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.mjs\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Heart!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.mjs\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const links = {\n        main: [\n            {\n                name: \"Calculator\",\n                href: \"/\"\n            },\n            {\n                name: \"Crops Database\",\n                href: \"/crops\"\n            },\n            {\n                name: \"Mutations Guide\",\n                href: \"/mutations\"\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t bg-muted/30 mt-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-primary-foreground font-bold text-lg\",\n                                                children: \"G\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 28,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg\",\n                                                children: \"Grow A Garden Calculator\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground text-sm leading-relaxed mb-4\",\n                                    children: \"The ultimate crop value calculator designed for Roblox Grow A Garden players. Calculate precise crop values with mutations, weights, and friend boosts to optimize your farming strategy.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Made with\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4 mx-1 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"for Grow A Garden players\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3\",\n                                    children: \"Main Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: links.main.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: link.href,\n                                                className: \"text-sm text-muted-foreground hover:text-foreground transition-colors\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-3\",\n                                    children: \"Game Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://www.roblox.com/games/grow-a-garden\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1\",\n                                            children: [\n                                                \"Play Grow A Garden\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n                    className: \"my-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row justify-between items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-muted-foreground text-center sm:text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"\\xa9 \",\n                                        currentYear,\n                                        \" Grow A Garden Calculator. All rights reserved.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1\",\n                                    children: \"This website is not affiliated with Gamer Robot Inc or the Grow A Garden game.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: \"Free online calculator tool\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/main-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/main-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MainLayout: () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./navbar */ \"(ssr)/./src/components/layout/navbar.tsx\");\n/* harmony import */ var _footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./footer */ \"(ssr)/./src/components/layout/footer.tsx\");\n/* __next_internal_client_entry_do_not_use__ MainLayout auto */ \n\n\n\nfunction MainLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_navbar__WEBPACK_IMPORTED_MODULE_2__.Navbar, {}, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvbWFpbi1sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ1E7QUFDQTtBQU0zQixTQUFTRyxXQUFXLEVBQUVDLFFBQVEsRUFBbUI7SUFDdEQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDTCwyQ0FBTUE7Ozs7OzBCQUNQLDhEQUFDTTtnQkFBS0QsV0FBVTswQkFDYkY7Ozs7OzswQkFFSCw4REFBQ0YsMkNBQU1BOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3Jvdy1hLWdhcmRlbi1jYWxjdWxhdG9yLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L21haW4tbGF5b3V0LnRzeD8wNGQ0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IE5hdmJhciB9IGZyb20gJy4vbmF2YmFyJztcbmltcG9ydCB7IEZvb3RlciB9IGZyb20gJy4vZm9vdGVyJztcblxuaW50ZXJmYWNlIE1haW5MYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBNYWluTGF5b3V0KHsgY2hpbGRyZW4gfTogTWFpbkxheW91dFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBmbGV4LWNvbFwiPlxuICAgICAgPE5hdmJhciAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvbWFpbj5cbiAgICAgIDxGb290ZXIgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIk5hdmJhciIsIkZvb3RlciIsIk1haW5MYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/main-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/navbar.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/navbar.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navbar: () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Menu,Sparkles,Sprout,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Menu,Sparkles,Sprout,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sprout.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Menu,Sparkles,Sprout,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Menu,Sparkles,Sprout,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,Menu,Sparkles,Sprout,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.mjs\");\n/* __next_internal_client_entry_do_not_use__ Navbar auto */ \n\n\n\n\n\n\nfunction Navbar() {\n    const [isMenuOpen, setIsMenuOpen] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: \"Calculator\",\n            href: \"/\",\n            icon: _barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Crop Value Calculator\"\n        },\n        {\n            name: \"Crops\",\n            href: \"/crops\",\n            icon: _barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Crop Database\"\n        },\n        {\n            name: \"Mutations\",\n            href: \"/mutations\",\n            icon: _barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Mutation System\"\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === \"/\") {\n            return pathname === \"/\";\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-foreground font-bold text-lg\",\n                                        children: \"G\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-lg\",\n                                            children: \"Grow A Garden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground ml-2 text-sm\",\n                                            children: \"Calculator\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-1\",\n                            children: navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: isActive(item.href) ? \"default\" : \"ghost\",\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_Menu_Sparkles_Sprout_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 57\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: navigation.map((item)=>{\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: isActive(item.href) ? \"default\" : \"ghost\",\n                                        className: \"w-full justify-start space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 23\n                                    }, this)\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 21\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\layout\\\\navbar.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwwUkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNsQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxNQUFNQyxPQUFPSCxVQUFVcEIsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUN1QjtRQUNDSixXQUFXakIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU87UUFBVTtRQUN4REcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixPQUFPTyxXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb3ctYS1nYXJkZW4tY2FsY3VsYXRvci8uL3NyYy9jb21wb25lbnRzL3VpL2J1dHRvbi50c3g/NmEwYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgU2xvdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2xvdFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1wcmltYXJ5LzkwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYmctZGVzdHJ1Y3RpdmUgdGV4dC1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kIGhvdmVyOmJnLWRlc3RydWN0aXZlLzkwXCIsXG4gICAgICAgIG91dGxpbmU6XG4gICAgICAgICAgXCJib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmRcIixcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgIFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgaG92ZXI6Ymctc2Vjb25kYXJ5LzgwXCIsXG4gICAgICAgIGdob3N0OiBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIGxpbms6IFwidGV4dC1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC0xMCBweC00IHB5LTJcIixcbiAgICAgICAgc206IFwiaC05IHJvdW5kZWQtbWQgcHgtM1wiLFxuICAgICAgICBsZzogXCJoLTExIHJvdW5kZWQtbWQgcHgtOFwiLFxuICAgICAgICBpY29uOiBcImgtMTAgdy0xMFwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5CdXR0b25IVE1MQXR0cmlidXRlczxIVE1MQnV0dG9uRWxlbWVudD4sXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xuICBhc0NoaWxkPzogYm9vbGVhblxufVxuXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdmFyaWFudCwgc2l6ZSwgYXNDaGlsZCA9IGZhbHNlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImJ1dHRvblwiXG4gICAgcmV0dXJuIChcbiAgICAgIDxDb21wXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiQnV0dG9uXCJcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbG90IiwiY3ZhIiwiY24iLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzZWNvbmRhcnkiLCJnaG9zdCIsImxpbmsiLCJzaXplIiwic20iLCJsZyIsImljb24iLCJkZWZhdWx0VmFyaWFudHMiLCJCdXR0b24iLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsInByb3BzIiwicmVmIiwiQ29tcCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb3ctYS1nYXJkZW4tY2FsY3VsYXRvci8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9jOTgzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/separator.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/separator.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"horizontal\", decorative = true, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined));\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9zZXBhcmF0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQThCO0FBQ2lDO0FBRS9CO0FBRWhDLE1BQU1HLDBCQUFZSCw2Q0FBZ0IsQ0FJaEMsQ0FDRSxFQUFFSyxTQUFTLEVBQUVDLGNBQWMsWUFBWSxFQUFFQyxhQUFhLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQ3RFQyxvQkFFQSw4REFBQ1IsMkRBQXVCO1FBQ3RCUSxLQUFLQTtRQUNMRixZQUFZQTtRQUNaRCxhQUFhQTtRQUNiRCxXQUFXSCw4Q0FBRUEsQ0FDWCxzQkFDQUksZ0JBQWdCLGVBQWUsbUJBQW1CLGtCQUNsREQ7UUFFRCxHQUFHRyxLQUFLOzs7Ozs7QUFJZkwsVUFBVVEsV0FBVyxHQUFHViwyREFBdUIsQ0FBQ1UsV0FBVztBQUV2QyIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb3ctYS1nYXJkZW4tY2FsY3VsYXRvci8uL3NyYy9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3g/ODRjOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgU2VwYXJhdG9yUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2VwYXJhdG9yXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBTZXBhcmF0b3IgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBTZXBhcmF0b3JQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgU2VwYXJhdG9yUHJpbWl0aXZlLlJvb3Q+XG4+KFxuICAoXG4gICAgeyBjbGFzc05hbWUsIG9yaWVudGF0aW9uID0gXCJob3Jpem9udGFsXCIsIGRlY29yYXRpdmUgPSB0cnVlLCAuLi5wcm9wcyB9LFxuICAgIHJlZlxuICApID0+IChcbiAgICA8U2VwYXJhdG9yUHJpbWl0aXZlLlJvb3RcbiAgICAgIHJlZj17cmVmfVxuICAgICAgZGVjb3JhdGl2ZT17ZGVjb3JhdGl2ZX1cbiAgICAgIG9yaWVudGF0aW9uPXtvcmllbnRhdGlvbn1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwic2hyaW5rLTAgYmctYm9yZGVyXCIsXG4gICAgICAgIG9yaWVudGF0aW9uID09PSBcImhvcml6b250YWxcIiA/IFwiaC1bMXB4XSB3LWZ1bGxcIiA6IFwiaC1mdWxsIHctWzFweF1cIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbilcblNlcGFyYXRvci5kaXNwbGF5TmFtZSA9IFNlcGFyYXRvclByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IFNlcGFyYXRvciB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTZXBhcmF0b3JQcmltaXRpdmUiLCJjbiIsIlNlcGFyYXRvciIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJvcmllbnRhdGlvbiIsImRlY29yYXRpdmUiLCJwcm9wcyIsInJlZiIsIlJvb3QiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/separator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/crops.ts":
/*!***************************!*\
  !*** ./src/data/crops.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crops: () => (/* binding */ crops),\n/* harmony export */   cropsByTier: () => (/* binding */ cropsByTier),\n/* harmony export */   getCropById: () => (/* binding */ getCropById),\n/* harmony export */   searchCrops: () => (/* binding */ searchCrops)\n/* harmony export */ });\nconst crops = [\n    // Common Tier\n    {\n        id: \"carrot\",\n        name: \"Carrot\",\n        image: \"https://img.growagardencalculator.net/crops/Carrot.webp\",\n        shecklePrice: 10,\n        robuxPrice: 7,\n        minimumValue: 18,\n        minimumWeight: 0.24,\n        tier: \"Common\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 312.5\n    },\n    {\n        id: \"strawberry\",\n        name: \"Strawberry\",\n        image: \"https://img.growagardencalculator.net/crops/Strawberry.webp\",\n        shecklePrice: 50,\n        robuxPrice: 21,\n        minimumValue: 14,\n        minimumWeight: 0.29,\n        tier: \"Common\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 166.4\n    },\n    {\n        id: \"chocolate-carrot\",\n        name: \"Chocolate Carrot\",\n        image: \"https://img.growagardencalculator.net/crops/Chocolate-Carrot.webp\",\n        shecklePrice: 75,\n        robuxPrice: 25,\n        minimumValue: 20,\n        minimumWeight: 0.32,\n        tier: \"Common\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 195.3\n    },\n    {\n        id: \"pink-tulip\",\n        name: \"Pink Tulip\",\n        image: \"https://img.growagardencalculator.net/crops/Pink-Tulip.webp\",\n        shecklePrice: 100,\n        robuxPrice: 29,\n        minimumValue: 25,\n        minimumWeight: 0.35,\n        tier: \"Common\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 204.1\n    },\n    // Uncommon Tier\n    {\n        id: \"blueberry\",\n        name: \"Blueberry\",\n        image: \"https://img.growagardencalculator.net/crops/Blueberry.webp\",\n        shecklePrice: 400,\n        robuxPrice: 49,\n        minimumValue: 18,\n        minimumWeight: 0.17,\n        tier: \"Uncommon\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 623.5\n    },\n    {\n        id: \"orange-tulip\",\n        name: \"Orange Tulip\",\n        image: \"https://img.growagardencalculator.net/crops/Orange-Tulip.webp\",\n        shecklePrice: 600,\n        robuxPrice: 14,\n        minimumValue: 751,\n        minimumWeight: 0.05,\n        tier: \"Uncommon\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 300400\n    },\n    {\n        id: \"lavender\",\n        name: \"Lavender\",\n        image: \"https://img.growagardencalculator.net/crops/Lavender.webp\",\n        shecklePrice: 650,\n        robuxPrice: 16,\n        minimumValue: 800,\n        minimumWeight: 0.06,\n        tier: \"Uncommon\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 222222\n    },\n    {\n        id: \"stonebite\",\n        name: \"Stonebite\",\n        image: \"https://img.growagardencalculator.net/crops/Stonebite.webp\",\n        shecklePrice: 700,\n        robuxPrice: 18,\n        minimumValue: 850,\n        minimumWeight: 0.07,\n        tier: \"Uncommon\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 173469\n    },\n    {\n        id: \"crocus\",\n        name: \"Crocus\",\n        image: \"https://img.growagardencalculator.net/crops/Crocus.webp\",\n        shecklePrice: 750,\n        robuxPrice: 20,\n        minimumValue: 900,\n        minimumWeight: 0.08,\n        tier: \"Uncommon\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 140625\n    },\n    {\n        id: \"wild-carrot\",\n        name: \"Wild Carrot\",\n        image: \"https://img.growagardencalculator.net/crops/Wild-Carrot.webp\",\n        shecklePrice: 800,\n        robuxPrice: 22,\n        minimumValue: 950,\n        minimumWeight: 0.09,\n        tier: \"Uncommon\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 117284\n    },\n    {\n        id: \"rose\",\n        name: \"Rose\",\n        image: \"https://img.growagardencalculator.net/crops/Rose.webp\",\n        shecklePrice: 850,\n        robuxPrice: 24,\n        minimumValue: 1000,\n        minimumWeight: 0.10,\n        tier: \"Uncommon\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 100000\n    },\n    {\n        id: \"nightshade\",\n        name: \"Nightshade\",\n        image: \"https://img.growagardencalculator.net/crops/Nightshade.webp\",\n        shecklePrice: 900,\n        robuxPrice: 26,\n        minimumValue: 1050,\n        minimumWeight: 0.11,\n        tier: \"Uncommon\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 86777\n    },\n    {\n        id: \"red-lollipop\",\n        name: \"Red Lollipop\",\n        image: \"https://img.growagardencalculator.net/crops/Red-Lollipop.webp\",\n        shecklePrice: 950,\n        robuxPrice: 28,\n        minimumValue: 1100,\n        minimumWeight: 0.12,\n        tier: \"Uncommon\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 76389\n    },\n    {\n        id: \"manuka-flower\",\n        name: \"Manuka Flower\",\n        image: \"https://img.growagardencalculator.net/crops/Manuka-Flower.webp\",\n        shecklePrice: 1000,\n        robuxPrice: 30,\n        minimumValue: 1150,\n        minimumWeight: 0.13,\n        tier: \"Uncommon\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 68047\n    },\n    {\n        id: \"blue-lollipop\",\n        name: \"Blue Lollipop\",\n        image: \"https://img.growagardencalculator.net/crops/Blue-Lollipop.webp\",\n        shecklePrice: 1050,\n        robuxPrice: 32,\n        minimumValue: 1200,\n        minimumWeight: 0.14,\n        tier: \"Uncommon\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 61224\n    },\n    // Rare Tier\n    {\n        id: \"tomato\",\n        name: \"Tomato\",\n        image: \"https://img.growagardencalculator.net/crops/Tomato.webp\",\n        shecklePrice: 800,\n        robuxPrice: 79,\n        minimumValue: 27,\n        minimumWeight: 0.44,\n        tier: \"Rare\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 139.7\n    },\n    {\n        id: \"corn\",\n        name: \"Corn\",\n        image: \"https://img.growagardencalculator.net/crops/Corn.webp\",\n        shecklePrice: 1300,\n        robuxPrice: 135,\n        minimumValue: 36,\n        minimumWeight: 1.9,\n        tier: \"Rare\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 9.97\n    },\n    {\n        id: \"daffodil\",\n        name: \"Daffodil\",\n        image: \"https://img.growagardencalculator.net/crops/Daffodil.webp\",\n        shecklePrice: 1000,\n        robuxPrice: 19,\n        minimumValue: 903,\n        minimumWeight: 0.16,\n        tier: \"Rare\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 35273.4\n    },\n    {\n        id: \"watermelon\",\n        name: \"Watermelon\",\n        image: \"https://img.growagardencalculator.net/crops/Watermelon.webp\",\n        shecklePrice: 2500,\n        robuxPrice: 195,\n        minimumValue: 2708,\n        minimumWeight: 7.3,\n        tier: \"Rare\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 50.8\n    },\n    {\n        id: \"liberty-lily\",\n        name: \"Liberty Lily\",\n        image: \"https://img.growagardencalculator.net/crops/Liberty-Lily.webp\",\n        shecklePrice: 2700,\n        robuxPrice: 205,\n        minimumValue: 2850,\n        minimumWeight: 7.6,\n        tier: \"Rare\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 49.3\n    },\n    {\n        id: \"nectarshade\",\n        name: \"Nectarshade\",\n        image: \"https://img.growagardencalculator.net/crops/Nectarshade.webp\",\n        shecklePrice: 2900,\n        robuxPrice: 215,\n        minimumValue: 3000,\n        minimumWeight: 7.9,\n        tier: \"Rare\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 48.1\n    },\n    {\n        id: \"delphinium\",\n        name: \"Delphinium\",\n        image: \"https://img.growagardencalculator.net/crops/Delphinium.webp\",\n        shecklePrice: 3100,\n        robuxPrice: 225,\n        minimumValue: 3150,\n        minimumWeight: 8.2,\n        tier: \"Rare\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 46.9\n    },\n    {\n        id: \"paradise-petal\",\n        name: \"Paradise Petal\",\n        image: \"https://img.growagardencalculator.net/crops/Paradise-Petal.webp\",\n        shecklePrice: 3300,\n        robuxPrice: 235,\n        minimumValue: 3300,\n        minimumWeight: 8.5,\n        tier: \"Rare\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 45.8\n    },\n    {\n        id: \"succulent\",\n        name: \"Succulent\",\n        image: \"https://img.growagardencalculator.net/crops/Succulent.webp\",\n        shecklePrice: 3500,\n        robuxPrice: 245,\n        minimumValue: 3450,\n        minimumWeight: 8.8,\n        tier: \"Rare\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 44.5\n    },\n    {\n        id: \"pear\",\n        name: \"Pear\",\n        image: \"https://img.growagardencalculator.net/crops/Pear.webp\",\n        shecklePrice: 3700,\n        robuxPrice: 255,\n        minimumValue: 3600,\n        minimumWeight: 9.1,\n        tier: \"Rare\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 43.5\n    },\n    {\n        id: \"foxglove\",\n        name: \"Foxglove\",\n        image: \"https://img.growagardencalculator.net/crops/Foxglove.webp\",\n        shecklePrice: 3900,\n        robuxPrice: 265,\n        minimumValue: 3750,\n        minimumWeight: 9.4,\n        tier: \"Rare\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 42.4\n    },\n    {\n        id: \"glowshroom\",\n        name: \"Glowshroom\",\n        image: \"https://img.growagardencalculator.net/crops/Glowshroom.webp\",\n        shecklePrice: 4100,\n        robuxPrice: 275,\n        minimumValue: 3900,\n        minimumWeight: 9.7,\n        tier: \"Rare\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 41.4\n    },\n    {\n        id: \"mint\",\n        name: \"Mint\",\n        image: \"https://img.growagardencalculator.net/crops/Mint.webp\",\n        shecklePrice: 4300,\n        robuxPrice: 285,\n        minimumValue: 4050,\n        minimumWeight: 10.0,\n        tier: \"Rare\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 40.5\n    },\n    {\n        id: \"raspberry\",\n        name: \"Raspberry\",\n        image: \"https://img.growagardencalculator.net/crops/Raspberry.webp\",\n        shecklePrice: 4500,\n        robuxPrice: 295,\n        minimumValue: 4200,\n        minimumWeight: 10.3,\n        tier: \"Rare\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 39.6\n    },\n    // Legendary Tier\n    {\n        id: \"pumpkin\",\n        name: \"Pumpkin\",\n        image: \"https://img.growagardencalculator.net/crops/Pumpkin.webp\",\n        shecklePrice: 3000,\n        robuxPrice: 210,\n        minimumValue: 3069,\n        minimumWeight: 6.9,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 64.5\n    },\n    {\n        id: \"apple\",\n        name: \"Apple\",\n        image: \"https://img.growagardencalculator.net/crops/Apple.webp\",\n        shecklePrice: 3250,\n        robuxPrice: 390,\n        minimumValue: 248,\n        minimumWeight: 2.85,\n        tier: \"Legendary\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 30.5\n    },\n    {\n        id: \"bamboo\",\n        name: \"Bamboo\",\n        image: \"https://img.growagardencalculator.net/crops/Bamboo.webp\",\n        shecklePrice: 4000,\n        robuxPrice: 99,\n        minimumValue: 3610,\n        minimumWeight: 3.8,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 249.7\n    },\n    {\n        id: \"firework-flower\",\n        name: \"Firework Flower\",\n        image: \"https://img.growagardencalculator.net/crops/Firework-Flower.webp\",\n        shecklePrice: 4500,\n        robuxPrice: 109,\n        minimumValue: 3900,\n        minimumWeight: 4.1,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 232.0\n    },\n    {\n        id: \"horned-dinoshroom\",\n        name: \"Horned Dinoshroom\",\n        image: \"https://img.growagardencalculator.net/crops/Horned-Dinoshroom.webp\",\n        shecklePrice: 5000,\n        robuxPrice: 119,\n        minimumValue: 4200,\n        minimumWeight: 4.4,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 217.0\n    },\n    {\n        id: \"boneboo\",\n        name: \"Boneboo\",\n        image: \"https://img.growagardencalculator.net/crops/Boneboo.webp\",\n        shecklePrice: 5500,\n        robuxPrice: 129,\n        minimumValue: 4500,\n        minimumWeight: 4.7,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 203.8\n    },\n    {\n        id: \"violet-corn\",\n        name: \"Violet Corn\",\n        image: \"https://img.growagardencalculator.net/crops/Violet-Corn.webp\",\n        shecklePrice: 6000,\n        robuxPrice: 139,\n        minimumValue: 4800,\n        minimumWeight: 5.0,\n        tier: \"Legendary\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 192.0\n    },\n    {\n        id: \"cantaloupe\",\n        name: \"Cantaloupe\",\n        image: \"https://img.growagardencalculator.net/crops/Cantaloupe.webp\",\n        shecklePrice: 6500,\n        robuxPrice: 149,\n        minimumValue: 5100,\n        minimumWeight: 5.3,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 181.5\n    },\n    {\n        id: \"lilac\",\n        name: \"Lilac\",\n        image: \"https://img.growagardencalculator.net/crops/Lilac.webp\",\n        shecklePrice: 7000,\n        robuxPrice: 159,\n        minimumValue: 5400,\n        minimumWeight: 5.6,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 172.3\n    },\n    {\n        id: \"moonflower\",\n        name: \"Moonflower\",\n        image: \"https://img.growagardencalculator.net/crops/Moonflower.webp\",\n        shecklePrice: 7500,\n        robuxPrice: 169,\n        minimumValue: 5700,\n        minimumWeight: 5.9,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 163.9\n    },\n    {\n        id: \"starfruit\",\n        name: \"Starfruit\",\n        image: \"https://img.growagardencalculator.net/crops/Starfruit.webp\",\n        shecklePrice: 8000,\n        robuxPrice: 179,\n        minimumValue: 6000,\n        minimumWeight: 6.2,\n        tier: \"Legendary\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 156.3\n    },\n    {\n        id: \"papaya\",\n        name: \"Papaya\",\n        image: \"https://img.growagardencalculator.net/crops/Papaya.webp\",\n        shecklePrice: 8500,\n        robuxPrice: 189,\n        minimumValue: 6300,\n        minimumWeight: 6.5,\n        tier: \"Legendary\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 149.2\n    },\n    {\n        id: \"cranberry\",\n        name: \"Cranberry\",\n        image: \"https://img.growagardencalculator.net/crops/Cranberry.webp\",\n        shecklePrice: 9000,\n        robuxPrice: 199,\n        minimumValue: 6600,\n        minimumWeight: 6.8,\n        tier: \"Legendary\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 142.9\n    },\n    {\n        id: \"durian\",\n        name: \"Durian\",\n        image: \"https://img.growagardencalculator.net/crops/Durian.webp\",\n        shecklePrice: 9500,\n        robuxPrice: 209,\n        minimumValue: 6900,\n        minimumWeight: 7.1,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 137.0\n    },\n    {\n        id: \"rafflesia\",\n        name: \"Rafflesia\",\n        image: \"https://img.growagardencalculator.net/crops/Rafflesia.webp\",\n        shecklePrice: 10000,\n        robuxPrice: 219,\n        minimumValue: 7200,\n        minimumWeight: 7.4,\n        tier: \"Legendary\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 131.6\n    },\n    // Mythical Tier\n    {\n        id: \"coconut\",\n        name: \"Coconut\",\n        image: \"https://img.growagardencalculator.net/crops/Coconut.webp\",\n        shecklePrice: 6000,\n        robuxPrice: 435,\n        minimumValue: 361,\n        minimumWeight: 13.31,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 2.04\n    },\n    {\n        id: \"cactus\",\n        name: \"Cactus\",\n        image: \"https://img.growagardencalculator.net/crops/Cactus.webp\",\n        shecklePrice: 15000,\n        robuxPrice: 497,\n        minimumValue: 3069,\n        minimumWeight: 6.65,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 69.4\n    },\n    {\n        id: \"dragon-fruit\",\n        name: \"Dragon Fruit\",\n        image: \"https://img.growagardencalculator.net/crops/Dragon-Fruit.webp\",\n        shecklePrice: 50000,\n        robuxPrice: 597,\n        minimumValue: 4287,\n        minimumWeight: 11.38,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 33.1\n    },\n    {\n        id: \"mango\",\n        name: \"Mango\",\n        image: \"https://img.growagardencalculator.net/crops/Mango.webp\",\n        shecklePrice: 100000,\n        robuxPrice: 580,\n        minimumValue: 5866,\n        minimumWeight: 14.28,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 28.8\n    },\n    {\n        id: \"nectarine\",\n        name: \"Nectarine\",\n        image: \"https://img.growagardencalculator.net/crops/Nectarine.webp\",\n        shecklePrice: 120000,\n        robuxPrice: 590,\n        minimumValue: 6200,\n        minimumWeight: 15.1,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 27.2\n    },\n    {\n        id: \"celestiberry\",\n        name: \"Celestiberry\",\n        image: \"https://img.growagardencalculator.net/crops/Celestiberry.webp\",\n        shecklePrice: 150000,\n        robuxPrice: 610,\n        minimumValue: 6800,\n        minimumWeight: 16.5,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 25.0\n    },\n    {\n        id: \"blood-banana\",\n        name: \"Blood Banana\",\n        image: \"https://img.growagardencalculator.net/crops/Blood-Banana.webp\",\n        shecklePrice: 180000,\n        robuxPrice: 630,\n        minimumValue: 7500,\n        minimumWeight: 18.0,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 23.1\n    },\n    {\n        id: \"moon-melon\",\n        name: \"Moon Melon\",\n        image: \"https://img.growagardencalculator.net/crops/Moon-Melon.webp\",\n        shecklePrice: 220000,\n        robuxPrice: 650,\n        minimumValue: 8300,\n        minimumWeight: 19.8,\n        tier: \"Mythical\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 21.2\n    },\n    {\n        id: \"lily-of-the-valley\",\n        name: \"Lily Of The Valley\",\n        image: \"https://img.growagardencalculator.net/crops/Lily-Of-The-Valley.webp\",\n        shecklePrice: 260000,\n        robuxPrice: 670,\n        minimumValue: 9200,\n        minimumWeight: 21.5,\n        tier: \"Mythical\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 19.9\n    },\n    {\n        id: \"firefly-fern\",\n        name: \"Firefly Fern\",\n        image: \"https://img.growagardencalculator.net/crops/Firefly-Fern.webp\",\n        shecklePrice: 300000,\n        robuxPrice: 690,\n        minimumValue: 10200,\n        minimumWeight: 23.4,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 18.6\n    },\n    {\n        id: \"bendboo\",\n        name: \"Bendboo\",\n        image: \"https://img.growagardencalculator.net/crops/Bendboo.webp\",\n        shecklePrice: 350000,\n        robuxPrice: 710,\n        minimumValue: 11300,\n        minimumWeight: 25.6,\n        tier: \"Mythical\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 17.2\n    },\n    {\n        id: \"cocovine\",\n        name: \"Cocovine\",\n        image: \"https://img.growagardencalculator.net/crops/Cocovine.webp\",\n        shecklePrice: 400000,\n        robuxPrice: 730,\n        minimumValue: 12500,\n        minimumWeight: 27.9,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 16.1\n    },\n    {\n        id: \"parasol-flower\",\n        name: \"Parasol Flower\",\n        image: \"https://img.growagardencalculator.net/crops/Parasol-Flower.webp\",\n        shecklePrice: 450000,\n        robuxPrice: 750,\n        minimumValue: 13800,\n        minimumWeight: 30.4,\n        tier: \"Mythical\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 14.9\n    },\n    {\n        id: \"pink-lily\",\n        name: \"Pink Lily\",\n        image: \"https://img.growagardencalculator.net/crops/Pink-Lily.webp\",\n        shecklePrice: 500000,\n        robuxPrice: 770,\n        minimumValue: 15200,\n        minimumWeight: 33.1,\n        tier: \"Mythical\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 13.9\n    },\n    {\n        id: \"purple-dahlia\",\n        name: \"Purple Dahlia\",\n        image: \"https://img.growagardencalculator.net/crops/Purple-Dahlia.webp\",\n        shecklePrice: 550000,\n        robuxPrice: 790,\n        minimumValue: 16800,\n        minimumWeight: 35.9,\n        tier: \"Mythical\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 13.0\n    },\n    {\n        id: \"moonglow\",\n        name: \"Moonglow\",\n        image: \"https://img.growagardencalculator.net/crops/Moonglow.webp\",\n        shecklePrice: 600000,\n        robuxPrice: 810,\n        minimumValue: 18500,\n        minimumWeight: 38.9,\n        tier: \"Mythical\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 12.2\n    },\n    // Divine Tier\n    {\n        id: \"grape\",\n        name: \"Grape\",\n        image: \"https://img.growagardencalculator.net/crops/Grape.webp\",\n        shecklePrice: 850000,\n        robuxPrice: 599,\n        minimumValue: 7085,\n        minimumWeight: 2.85,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 872.2\n    },\n    {\n        id: \"mushroom\",\n        name: \"Mushroom\",\n        image: \"https://img.growagardencalculator.net/crops/Mushroom.webp\",\n        shecklePrice: 150000,\n        robuxPrice: 249,\n        minimumValue: 136278,\n        minimumWeight: 25.9,\n        tier: \"Divine\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 203.2\n    },\n    {\n        id: \"pepper\",\n        name: \"Pepper\",\n        image: \"https://img.growagardencalculator.net/crops/Pepper.webp\",\n        shecklePrice: 1000000,\n        robuxPrice: 629,\n        minimumValue: 7220,\n        minimumWeight: 4.75,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 320.0\n    },\n    {\n        id: \"cacao\",\n        name: \"Cacao\",\n        image: \"https://img.growagardencalculator.net/crops/Cacao.webp\",\n        shecklePrice: 2500000,\n        robuxPrice: 679,\n        minimumValue: 10830,\n        minimumWeight: 7.6,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 187.5\n    },\n    {\n        id: \"hive-fruit\",\n        name: \"Hive Fruit\",\n        image: \"https://img.growagardencalculator.net/crops/Hive-Fruit.webp\",\n        shecklePrice: 3000000,\n        robuxPrice: 699,\n        minimumValue: 12500,\n        minimumWeight: 8.2,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 186.0\n    },\n    {\n        id: \"moon-mango\",\n        name: \"Moon Mango\",\n        image: \"https://img.growagardencalculator.net/crops/Moon-Mango.webp\",\n        shecklePrice: 3500000,\n        robuxPrice: 729,\n        minimumValue: 14200,\n        minimumWeight: 9.1,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 171.5\n    },\n    {\n        id: \"travelers-fruit\",\n        name: \"Traveler's Fruit\",\n        image: \"https://img.growagardencalculator.net/crops/Travelers-Fruit.webp\",\n        shecklePrice: 4000000,\n        robuxPrice: 759,\n        minimumValue: 16800,\n        minimumWeight: 10.5,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 152.4\n    },\n    {\n        id: \"fossilight\",\n        name: \"Fossilight\",\n        image: \"https://img.growagardencalculator.net/crops/Fossilight.webp\",\n        shecklePrice: 4500000,\n        robuxPrice: 789,\n        minimumValue: 18900,\n        minimumWeight: 11.8,\n        tier: \"Divine\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 135.8\n    },\n    {\n        id: \"dragon-pepper\",\n        name: \"Dragon Pepper\",\n        image: \"https://img.growagardencalculator.net/crops/Dragon-Pepper.webp\",\n        shecklePrice: 5000000,\n        robuxPrice: 819,\n        minimumValue: 21500,\n        minimumWeight: 12.9,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 129.3\n    },\n    {\n        id: \"rosy-delight\",\n        name: \"Rosy Delight\",\n        image: \"https://img.growagardencalculator.net/crops/Rosy-Delight.webp\",\n        shecklePrice: 5500000,\n        robuxPrice: 849,\n        minimumValue: 24200,\n        minimumWeight: 14.1,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 121.7\n    },\n    {\n        id: \"sunflower\",\n        name: \"Sunflower\",\n        image: \"https://img.growagardencalculator.net/crops/Sunflower.webp\",\n        shecklePrice: 6000000,\n        robuxPrice: 879,\n        minimumValue: 27100,\n        minimumWeight: 15.4,\n        tier: \"Divine\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 114.3\n    },\n    {\n        id: \"moon-blossom\",\n        name: \"Moon Blossom\",\n        image: \"https://img.growagardencalculator.net/crops/Moon-Blossom.webp\",\n        shecklePrice: 6500000,\n        robuxPrice: 909,\n        minimumValue: 30200,\n        minimumWeight: 16.8,\n        tier: \"Divine\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 107.1\n    },\n    {\n        id: \"soul-fruit\",\n        name: \"Soul Fruit\",\n        image: \"https://img.growagardencalculator.net/crops/Soul-Fruit.webp\",\n        shecklePrice: 7000000,\n        robuxPrice: 939,\n        minimumValue: 33500,\n        minimumWeight: 18.2,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 101.1\n    },\n    {\n        id: \"cursed-fruit\",\n        name: \"Cursed Fruit\",\n        image: \"https://img.growagardencalculator.net/crops/Cursed-Fruit.webp\",\n        shecklePrice: 7500000,\n        robuxPrice: 969,\n        minimumValue: 37000,\n        minimumWeight: 19.7,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 95.4\n    },\n    {\n        id: \"lotus\",\n        name: \"Lotus\",\n        image: \"https://img.growagardencalculator.net/crops/Lotus.webp\",\n        shecklePrice: 8000000,\n        robuxPrice: 999,\n        minimumValue: 40800,\n        minimumWeight: 21.3,\n        tier: \"Divine\",\n        harvestType: \"Single\",\n        obtainable: true,\n        k: 89.9\n    },\n    {\n        id: \"venus-fly-trap\",\n        name: \"Venus Fly Trap\",\n        image: \"https://img.growagardencalculator.net/crops/Venus-Fly-Trap.webp\",\n        shecklePrice: 8500000,\n        robuxPrice: 1029,\n        minimumValue: 44900,\n        minimumWeight: 22.9,\n        tier: \"Divine\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 85.7\n    },\n    // Prismatic Tier\n    {\n        id: \"beanstalk\",\n        name: \"Beanstalk\",\n        image: \"https://img.growagardencalculator.net/crops/Beanstalk.webp\",\n        shecklePrice: 10000000,\n        robuxPrice: 715,\n        minimumValue: 25270,\n        minimumWeight: 9.5,\n        tier: \"Prismatic\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 280.0\n    },\n    {\n        id: \"ember-lily\",\n        name: \"Ember Lily\",\n        image: \"https://img.growagardencalculator.net/crops/Ember-Lily.webp\",\n        shecklePrice: 15000000,\n        robuxPrice: 779,\n        minimumValue: 50138,\n        minimumWeight: 11.4,\n        tier: \"Prismatic\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 385.7\n    },\n    {\n        id: \"sugar-apple\",\n        name: \"Sugar Apple\",\n        image: \"https://img.growagardencalculator.net/crops/Sugar-Apple.webp\",\n        shecklePrice: 25000000,\n        robuxPrice: 819,\n        minimumValue: 43320,\n        minimumWeight: 8.55,\n        tier: \"Prismatic\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 592.6\n    },\n    {\n        id: \"burning-bud\",\n        name: \"Burning Bud\",\n        image: \"https://img.growagardencalculator.net/crops/Burning-Bud.webp\",\n        shecklePrice: 40000000,\n        robuxPrice: 915,\n        minimumValue: 63175,\n        minimumWeight: 11.4,\n        tier: \"Prismatic\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 486.2\n    },\n    {\n        id: \"giant-pinecone\",\n        name: \"Giant Pinecone\",\n        image: \"https://img.growagardencalculator.net/crops/Giant-Pinecone.webp\",\n        shecklePrice: 55000000,\n        robuxPrice: 929,\n        minimumValue: 64980,\n        minimumWeight: 5.14,\n        tier: \"Prismatic\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 2461.5\n    },\n    {\n        id: \"elephant-ears\",\n        name: \"Elephant Ears\",\n        image: \"https://img.growagardencalculator.net/crops/Elephant-Ears.webp\",\n        shecklePrice: 75000000,\n        robuxPrice: 999,\n        minimumValue: 85000,\n        minimumWeight: 6.2,\n        tier: \"Prismatic\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 2200.0\n    },\n    // Transcendent Tier\n    {\n        id: \"bone-blossom\",\n        name: \"Bone Blossom\",\n        image: \"https://img.growagardencalculator.net/crops/Bone-Blossom.webp\",\n        shecklePrice: 100000000,\n        robuxPrice: 1299,\n        minimumValue: 150000,\n        minimumWeight: 8.0,\n        tier: \"Transcendent\",\n        harvestType: \"Multi\",\n        obtainable: true,\n        k: 2343.75\n    }\n];\n// Group crops by tier\nconst cropsByTier = crops.reduce((acc, crop)=>{\n    if (!acc[crop.tier]) {\n        acc[crop.tier] = [];\n    }\n    acc[crop.tier].push(crop);\n    return acc;\n}, {});\n// Get crop by ID\nconst getCropById = (id)=>{\n    return crops.find((crop)=>crop.id === id);\n};\n// Search crops function\nconst searchCrops = (query)=>{\n    const lowercaseQuery = query.toLowerCase();\n    return crops.filter((crop)=>crop.name.toLowerCase().includes(lowercaseQuery) || crop.tier.toLowerCase().includes(lowercaseQuery));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/crops.ts\n");

/***/ }),

/***/ "(ssr)/./src/data/mutations.ts":
/*!*******************************!*\
  !*** ./src/data/mutations.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allMutations: () => (/* binding */ allMutations),\n/* harmony export */   checkMutationCompatibility: () => (/* binding */ checkMutationCompatibility),\n/* harmony export */   environmentalMutations: () => (/* binding */ environmentalMutations),\n/* harmony export */   getMutationById: () => (/* binding */ getMutationById),\n/* harmony export */   getMutationsByType: () => (/* binding */ getMutationsByType),\n/* harmony export */   growthMutations: () => (/* binding */ growthMutations),\n/* harmony export */   mutationConstraints: () => (/* binding */ mutationConstraints),\n/* harmony export */   temperatureMutations: () => (/* binding */ temperatureMutations)\n/* harmony export */ });\n// Growth Mutations\nconst growthMutations = [\n    {\n        id: \"golden\",\n        name: \"Golden\",\n        image: \"https://img.growagardencalculator.net/mutations/Golden.webp\",\n        multiplier: 20,\n        stackBonus: 0,\n        type: \"Growth\",\n        description: \"Has a 1% chance to replace the standard crop variant or can be applied by Dragonfly. Plants display a shining, golden appearance.\",\n        triggers: \"1% chance to replace standard crop variant or can be applied by Dragonfly\"\n    },\n    {\n        id: \"rainbow\",\n        name: \"Rainbow\",\n        image: \"https://img.growagardencalculator.net/mutations/Rainbow.webp\",\n        multiplier: 50,\n        stackBonus: 0,\n        type: \"Growth\",\n        description: \"Has a 0.1% chance to replace the standard crop variant or can be applied by Butterfly. Plants continuously cycle through colors, emitting yellow particles and displaying a rainbow above.\",\n        triggers: \"0.1% chance to replace standard crop variant or can be applied by Butterfly\"\n    }\n];\n// Temperature Mutations\nconst temperatureMutations = [\n    {\n        id: \"wet\",\n        name: \"Wet\",\n        image: \"https://img.growagardencalculator.net/mutations/Wet.webp\",\n        multiplier: 2,\n        stackBonus: 1,\n        type: \"Temperature\",\n        description: \"Triggered during Rain or Thunderstorm events, or with a low probability from Sprinklers. Affected plants exhibit water droplets on their surface.\",\n        triggers: \"Triggered during Rain or Thunderstorm events, or with a low probability from Sprinklers\"\n    },\n    {\n        id: \"chilled\",\n        name: \"Chilled\",\n        image: \"https://img.growagardencalculator.net/mutations/Chilled.webp\",\n        multiplier: 2,\n        stackBonus: 1,\n        type: \"Temperature\",\n        description: \"Occurs during Frost events or through interaction with a Polar Bear. Plants display a bluish tint and frost particles.\",\n        triggers: \"Occurs during Frost events or through interaction with a Polar Bear\"\n    },\n    {\n        id: \"drenched\",\n        name: \"Drenched\",\n        image: \"https://img.growagardencalculator.net/mutations/Drenched.webp\",\n        multiplier: 5,\n        stackBonus: 4,\n        type: \"Temperature\",\n        description: \"This mutation is applied to crops during a Tropical Rain event, where it supersedes the Wet mutation. Visually, it is characterized by large water droplets descending from the crop, which also exhibits a slightly saturated appearance.\",\n        triggers: \"Applied to crops during a Tropical Rain event\"\n    },\n    {\n        id: \"frozen\",\n        name: \"Frozen\",\n        image: \"https://img.growagardencalculator.net/mutations/Frozen.webp\",\n        multiplier: 10,\n        stackBonus: 9,\n        type: \"Temperature\",\n        description: \"Occurs during Frost when crops are both Wet and Chilled, or through Polar Bear interaction. Plants are encased in an ice block.\",\n        triggers: \"Occurs during Frost when crops are both Wet and Chilled, or through Polar Bear interaction\"\n    }\n];\n// Environmental Mutations\nconst environmentalMutations = [\n    {\n        id: \"choc\",\n        name: \"Choc\",\n        image: \"https://img.growagardencalculator.net/mutations/Choc.webp\",\n        multiplier: 2,\n        stackBonus: 1,\n        type: \"Environmental\",\n        description: \"Applied by placing a Chocolate Sprinkler, available during the Easter Event 2025 or admin-triggered Chocolate Rain events.\",\n        triggers: \"Applied by placing a Chocolate Sprinkler, available during Easter Event 2025\"\n    },\n    {\n        id: \"moonlit\",\n        name: \"Moonlit\",\n        image: \"https://img.growagardencalculator.net/mutations/Moonlit.webp\",\n        multiplier: 2,\n        stackBonus: 1,\n        type: \"Environmental\",\n        description: \"Activates at night, affecting six plants every two minutes within a 10-minute period. Plants emit a purple glow with a shining appearance.\",\n        triggers: \"Activates at night, affecting six plants every two minutes within a 10-minute period\"\n    },\n    {\n        id: \"windstruck\",\n        name: \"Windstruck\",\n        image: \"https://img.growagardencalculator.net/mutations/Windstruck.webp\",\n        multiplier: 2,\n        stackBonus: 1,\n        type: \"Environmental\",\n        description: \"Triggered during Windy or Gale conditions. With wind gusts swooping around the crop for a dynamic visual effect.\",\n        triggers: \"Triggered during Windy or Gale conditions\"\n    },\n    {\n        id: \"pollinated\",\n        name: \"Pollinated\",\n        image: \"https://img.growagardencalculator.net/mutations/Pollinated.webp\",\n        multiplier: 3,\n        stackBonus: 2,\n        type: \"Environmental\",\n        description: \"Triggered during Bee Swarm events or by specific bee pets (Bee, Honey Bee, Petal Bee, Queen Bee). Plants exhibit a yellow shine with yellow gas-like particles.\",\n        triggers: \"Triggered during Bee Swarm events or by specific bee pets\"\n    },\n    {\n        id: \"sandy\",\n        name: \"Sandy\",\n        image: \"https://img.growagardencalculator.net/mutations/Sandy.webp\",\n        multiplier: 3,\n        stackBonus: 2,\n        type: \"Environmental\",\n        description: \"Triggered during a Sandstorm event. The crop appears tan in color and emits puffs of sand around the fruit.\",\n        triggers: \"Triggered during a Sandstorm event\"\n    },\n    {\n        id: \"bloodlit\",\n        name: \"Bloodlit\",\n        image: \"https://img.growagardencalculator.net/mutations/Bloodlit.webp\",\n        multiplier: 4,\n        stackBonus: 3,\n        type: \"Environmental\",\n        description: \"Occurs during the Blood Moon Event. Affected plants display a red, radiant appearance.\",\n        triggers: \"Occurs during the Blood Moon Event\"\n    },\n    {\n        id: \"burnt\",\n        name: \"Burnt\",\n        image: \"https://img.growagardencalculator.net/mutations/Burnt.webp\",\n        multiplier: 4,\n        stackBonus: 3,\n        type: \"Environmental\",\n        description: \"Crops may be affected by the 'Burnt' mutation triggered by the Cooked Owl. Plants exhibiting this mutation will display a blackened model, accompanied by a sparking effect at the top when unharvested.\",\n        triggers: \"Triggered by the Cooked Owl\"\n    },\n    {\n        id: \"verdant\",\n        name: \"Verdant\",\n        image: \"https://img.growagardencalculator.net/mutations/Verdant.webp\",\n        multiplier: 4,\n        stackBonus: 3,\n        type: \"Environmental\",\n        description: \"May occasionally be triggered by the Scarlet Macaw pet. Showcasing a green hue and releasing green rectangular particles.\",\n        triggers: \"May occasionally be triggered by the Scarlet Macaw pet\"\n    },\n    {\n        id: \"wiltproof\",\n        name: \"Wiltproof\",\n        image: \"https://img.growagardencalculator.net/mutations/Wiltproof.webp\",\n        multiplier: 4,\n        stackBonus: 3,\n        type: \"Environmental\",\n        description: \"This mutation is obtainable during the Drought weather event. Its visual characteristics are currently unknown.\",\n        triggers: \"Obtainable during the Drought weather event\"\n    },\n    {\n        id: \"plasma\",\n        name: \"Plasma\",\n        image: \"https://img.growagardencalculator.net/mutations/Plasma.webp\",\n        multiplier: 5,\n        stackBonus: 4,\n        type: \"Environmental\",\n        description: \"Triggered during admin-spawned Laser Storm events. Plants exhibit a static pinkish-purple glow with intermittent red flashes.\",\n        triggers: \"Triggered during admin-spawned Laser Storm events\"\n    },\n    {\n        id: \"honeyglazed\",\n        name: \"HoneyGlazed\",\n        image: \"https://img.growagardencalculator.net/mutations/HoneyGlazed.webp\",\n        multiplier: 5,\n        stackBonus: 4,\n        type: \"Environmental\",\n        description: \"Applied by placing a Honey Sprinkler or through interaction with Bear Bee. Plants are surrounded by yellow fog and drip with yellow liquid.\",\n        triggers: \"Applied by placing a Honey Sprinkler or through interaction with Bear Bee\"\n    },\n    {\n        id: \"heavenly\",\n        name: \"Heavenly\",\n        image: \"https://img.growagardencalculator.net/mutations/Heavenly.webp\",\n        multiplier: 5,\n        stackBonus: 4,\n        type: \"Environmental\",\n        description: \"Triggered during admin-spawned Floating Jandel events. Plants emit golden, radiant light from their base.\",\n        triggers: \"Triggered during admin-spawned Floating Jandel events\"\n    },\n    {\n        id: \"twisted\",\n        name: \"Twisted\",\n        image: \"https://img.growagardencalculator.net/mutations/Twisted.webp\",\n        multiplier: 5,\n        stackBonus: 4,\n        type: \"Environmental\",\n        description: \"Activated during Tornado conditions, featuring tornado-like swirls emanating from the crop.\",\n        triggers: \"Activated during Tornado conditions\"\n    },\n    {\n        id: \"cloudtouched\",\n        name: \"Cloudtouched\",\n        image: \"https://img.growagardencalculator.net/mutations/Cloudtouched.webp\",\n        multiplier: 5,\n        stackBonus: 4,\n        type: \"Environmental\",\n        description: \"This mutation can be induced using the Mutation Spray Cloudtouched or has a low probability of being applied by the Hyacinth Macaw pet. The affected crop is enveloped in a distinct cloud-like aura.\",\n        triggers: \"Can be induced using Mutation Spray Cloudtouched or by Hyacinth Macaw pet\"\n    },\n    {\n        id: \"clay\",\n        name: \"Clay\",\n        image: \"https://img.growagardencalculator.net/mutations/Clay.webp\",\n        multiplier: 5,\n        stackBonus: 4,\n        type: \"Environmental\",\n        description: \"Occurs by combining the 'Wet' and 'Sandy' mutations. The crop appears a brownish color and unique texture.\",\n        triggers: \"Occurs by combining the Wet and Sandy mutations\"\n    },\n    {\n        id: \"shocked\",\n        name: \"Shocked\",\n        image: \"https://img.growagardencalculator.net/mutations/Shocked.webp\",\n        multiplier: 100,\n        stackBonus: 99,\n        type: \"Environmental\",\n        description: \"Triggered by lightning strikes during Thunderstorm events or Jandel Storm. Plants exhibit a bright, neon glow, lacking the typical studded texture.\",\n        triggers: \"Triggered by lightning strikes during Thunderstorm events or Jandel Storm\"\n    },\n    {\n        id: \"celestial\",\n        name: \"Celestial\",\n        image: \"https://img.growagardencalculator.net/mutations/Celestial.webp\",\n        multiplier: 120,\n        stackBonus: 119,\n        type: \"Environmental\",\n        description: \"Occurs during Meteor Shower events. Plants appear slightly discolored with sparkling yellow and purple effects.\",\n        triggers: \"Occurs during Meteor Shower events\"\n    },\n    {\n        id: \"disco\",\n        name: \"Disco\",\n        image: \"https://img.growagardencalculator.net/mutations/Disco.webp\",\n        multiplier: 125,\n        stackBonus: 124,\n        type: \"Environmental\",\n        description: \"Triggered during admin-spawned Disco events or via Disco Bee. Plants flash through red, pink, yellow, green, and blue colors instantly, distinct from the gradual color transitions of Rainbow.\",\n        triggers: \"Triggered during admin-spawned Disco events or via Disco Bee\"\n    },\n    {\n        id: \"dawnbound\",\n        name: \"Dawnbound\",\n        image: \"https://img.growagardencalculator.net/mutations/Dawnbound.webp\",\n        multiplier: 150,\n        stackBonus: 149,\n        type: \"Environmental\",\n        description: \"Triggered during admin-spawned Sun God Events or with low natural occurrence probability. Sunflowers emit neon yellow light.\",\n        triggers: \"Triggered during admin-spawned Sun God Events or with low natural occurrence probability\",\n        constraints: [\n            \"sunflower-only\"\n        ]\n    },\n    {\n        id: \"amber\",\n        name: \"Amber\",\n        image: \"https://img.growagardencalculator.net/mutations/Amber.webp\",\n        multiplier: 10,\n        stackBonus: 9,\n        type: \"Environmental\",\n        description: \"Applied via an Amber Mutation Spray or occasionally triggered by the Raptor pet during fruit harvest. The crop exhibits a semi-transparent orange coating and releases orange cloud-like particles.\",\n        triggers: \"Applied via an Amber Mutation Spray or occasionally triggered by the Raptor pet\"\n    },\n    {\n        id: \"oldamber\",\n        name: \"OldAmber\",\n        image: \"https://img.growagardencalculator.net/mutations/OldAmber.webp\",\n        multiplier: 20,\n        stackBonus: 19,\n        type: \"Environmental\",\n        description: \"Evolves from an Amber mutation after 24 hours of aging. It displays a more pronounced orange hue compared to its initial form.\",\n        triggers: \"Evolves from an Amber mutation after 24 hours of aging\"\n    },\n    {\n        id: \"ancientamber\",\n        name: \"AncientAmber\",\n        image: \"https://img.growagardencalculator.net/mutations/AncientAmber.webp\",\n        multiplier: 50,\n        stackBonus: 49,\n        type: \"Environmental\",\n        description: \"Emerges from an OldAmber mutation after an unspecified aging period. Its visual characteristics remain undocumented.\",\n        triggers: \"Emerges from an OldAmber mutation after an unspecified aging period\"\n    },\n    {\n        id: \"cooked\",\n        name: \"Cooked\",\n        image: \"https://img.growagardencalculator.net/mutations/Cooked.webp\",\n        multiplier: 10,\n        stackBonus: 9,\n        type: \"Environmental\",\n        description: \"There is a low probability that Cooked Owl will apply this mutation instead of Burnt. Affected plants will exhibit an orange coloration.\",\n        triggers: \"Low probability application by Cooked Owl instead of Burnt mutation\"\n    },\n    {\n        id: \"ceramic\",\n        name: \"Ceramic\",\n        image: \"https://img.growagardencalculator.net/mutations/Ceramic.webp\",\n        multiplier: 30,\n        stackBonus: 29,\n        type: \"Environmental\",\n        description: \"Requires the Clay mutation and one of the following: Molten, Sundried, Meteoric, Burnt, Fried, Cooked, or Plasma. Exhibits a darker hue, resembling the Sundried mutation.\",\n        triggers: \"Requires Clay mutation combined with heat-based mutations\"\n    },\n    {\n        id: \"fried\",\n        name: \"Fried\",\n        image: \"https://img.growagardencalculator.net/mutations/Fried.webp\",\n        multiplier: 8,\n        stackBonus: 7,\n        type: \"Environmental\",\n        description: \"Triggered during a Fried Chicken event, this mutation is visually identified by small yellow particles that fall from the affected crop.\",\n        triggers: \"Triggered during a Fried Chicken event\"\n    },\n    {\n        id: \"tempestuous\",\n        name: \"Tempestuous\",\n        image: \"https://img.growagardencalculator.net/mutations/Tempestuous.webp\",\n        multiplier: 14,\n        stackBonus: 13,\n        type: \"Environmental\",\n        description: \"By combining the Windstruck and Twisted mutations, players can obtain the Tempestuous mutation. With this mutation applied, your fruit will have white wind particle animations.\",\n        triggers: \"Combination of Windstruck and Twisted mutations\"\n    },\n    {\n        id: \"galactic\",\n        name: \"Galactic\",\n        image: \"https://img.growagardencalculator.net/mutations/Galactic.webp\",\n        multiplier: 120,\n        stackBonus: 119,\n        type: \"Environmental\",\n        description: \"Triggered during the Space Travel Event, showcasing a light purple or pink hue with neon accents and emitting pink sparkling glimmers.\",\n        triggers: \"Triggered during the Space Travel Event\"\n    },\n    {\n        id: \"voidtouched\",\n        name: \"Voidtouched\",\n        image: \"https://img.growagardencalculator.net/mutations/Voidtouched.webp\",\n        multiplier: 135,\n        stackBonus: 134,\n        type: \"Environmental\",\n        description: \"Occurs during admin-spawned Black Hole Events. Plants are surrounded by purple particles resembling miniature black holes, similar to Moonlit effects.\",\n        triggers: \"Occurs during admin-spawned Black Hole Events\"\n    },\n    {\n        id: \"meteoric\",\n        name: \"Meteoric\",\n        image: \"https://img.growagardencalculator.net/mutations/Meteoric.webp\",\n        multiplier: 125,\n        stackBonus: 124,\n        type: \"Environmental\",\n        description: \"Triggered during the Meteor Strike event, which can only be initiated by administrators.\",\n        triggers: \"Triggered during the Meteor Strike event\"\n    },\n    {\n        id: \"molten\",\n        name: \"Molten\",\n        image: \"https://img.growagardencalculator.net/mutations/Molten.webp\",\n        multiplier: 25,\n        stackBonus: 24,\n        type: \"Environmental\",\n        description: \"During the volcano event, crops may undergo a 'Molten' mutation. Affected plants will display vibrant orange, yellow, and red hues, with a neon glow akin to the Shocked mutation.\",\n        triggers: \"During the volcano event\"\n    },\n    {\n        id: \"zombified\",\n        name: \"Zombified\",\n        image: \"https://img.growagardencalculator.net/mutations/Zombified.webp\",\n        multiplier: 25,\n        stackBonus: 24,\n        type: \"Environmental\",\n        description: \"Caused by interaction with the unobtainable Chicken Zombie. Plants are surrounded by green fog and drip with green liquid.\",\n        triggers: \"Caused by interaction with the unobtainable Chicken Zombie\"\n    },\n    {\n        id: \"friendbound\",\n        name: \"Friendbound\",\n        image: \"https://img.growagardencalculator.net/mutations/Friendbound.webp\",\n        multiplier: 70,\n        stackBonus: 69,\n        type: \"Environmental\",\n        description: \"To obtain the Friendbound mutation, purchase the Friendship Pot from the Gear Shop and connect it with an active friend. This mutation causes your fruit to turn pink, emitting pink hearts and stars.\",\n        triggers: \"Purchase Friendship Pot and connect with an active friend\"\n    },\n    {\n        id: \"infected\",\n        name: \"Infected\",\n        image: \"https://img.growagardencalculator.net/mutations/Infected.webp\",\n        multiplier: 75,\n        stackBonus: 74,\n        type: \"Environmental\",\n        description: \"Triggered during the Jandel Zombie event. This mutation will emit small, hazy green particles.\",\n        triggers: \"Triggered during the Jandel Zombie event\"\n    },\n    {\n        id: \"sundried\",\n        name: \"Sundried\",\n        image: \"https://img.growagardencalculator.net/mutations/Sundried.webp\",\n        multiplier: 85,\n        stackBonus: 84,\n        type: \"Environmental\",\n        description: \"Triggered during the Heatwave event. Apply deep brown hue.\",\n        triggers: \"Triggered during the Heatwave event\"\n    },\n    {\n        id: \"aurora\",\n        name: \"Aurora\",\n        image: \"https://img.growagardencalculator.net/mutations/Aurora.webp\",\n        multiplier: 90,\n        stackBonus: 89,\n        type: \"Environmental\",\n        description: \"This mutation is conferred upon crops during the Aurora Borealis event. The affected crop pulsates between shades of blue and purple and emits a faint smoke from its upper portion.\",\n        triggers: \"Conferred during the Aurora Borealis event\"\n    },\n    {\n        id: \"paradisal\",\n        name: \"Paradisal\",\n        image: \"https://img.growagardencalculator.net/mutations/Paradisal.webp\",\n        multiplier: 100,\n        stackBonus: 99,\n        type: \"Environmental\",\n        description: \"Triggered by combining Verdant and Sundried mutations on the crop. Features vibrant tropical colors with lush, paradise-inspired effects.\",\n        triggers: \"Combination of Verdant and Sundried mutations\"\n    },\n    {\n        id: \"alienlike\",\n        name: \"Alienlike\",\n        image: \"https://img.growagardencalculator.net/mutations/Alienlike.webp\",\n        multiplier: 100,\n        stackBonus: 99,\n        type: \"Environmental\",\n        description: \"Activated during a Alien Invasion Event. Displaying a cyan hue with cyan particles emanating from the fruit, which may have fully or partially transparent sections.\",\n        triggers: \"Activated during a Alien Invasion Event\"\n    },\n    {\n        id: \"dawnbound\",\n        name: \"Dawnbound\",\n        image: \"https://img.growagardencalculator.net/mutations/Dawnbound.webp\",\n        multiplier: 150,\n        stackBonus: 149,\n        type: \"Environmental\",\n        description: \"Triggered during admin-spawned Sun God Events or with a low natural occurrence probability. Sunflowers with this mutation glow in neon yellow.\",\n        triggers: \"Triggered during admin-spawned Sun God Events or with low natural occurrence probability\"\n    }\n];\n// All mutations\nconst allMutations = [\n    ...growthMutations,\n    ...temperatureMutations,\n    ...environmentalMutations\n];\n// Mutation constraint rules\nconst mutationConstraints = [\n    {\n        group: \"growth\",\n        mutationIds: [\n            \"golden\",\n            \"rainbow\"\n        ],\n        maxAllowed: 1,\n        description: \"Only one growth mutation can be applied (Golden or Rainbow)\"\n    },\n    {\n        group: \"temperature\",\n        mutationIds: [\n            \"wet\",\n            \"chilled\",\n            \"drenched\",\n            \"frozen\"\n        ],\n        maxAllowed: 1,\n        description: \"Only one temperature mutation can be applied\"\n    },\n    {\n        group: \"cooking\",\n        mutationIds: [\n            \"burnt\",\n            \"cooked\"\n        ],\n        maxAllowed: 1,\n        description: \"Only one cooking mutation can be applied\"\n    },\n    {\n        group: \"amber\",\n        mutationIds: [\n            \"amber\",\n            \"oldamber\",\n            \"ancientamber\"\n        ],\n        maxAllowed: 1,\n        description: \"Only one amber mutation can be applied\"\n    },\n    {\n        group: \"earth\",\n        mutationIds: [\n            \"sandy\",\n            \"clay\"\n        ],\n        maxAllowed: 1,\n        description: \"Only one earth mutation can be applied\"\n    },\n    {\n        group: \"ceramic\",\n        mutationIds: [\n            \"clay\",\n            \"ceramic\"\n        ],\n        maxAllowed: 1,\n        description: \"Only one ceramic mutation can be applied\"\n    },\n    {\n        group: \"extreme\",\n        mutationIds: [\n            \"shocked\",\n            \"celestial\",\n            \"disco\",\n            \"voidtouched\"\n        ],\n        maxAllowed: 1,\n        description: \"Only one extreme mutation can be applied\"\n    }\n];\n// Get mutation by ID\nconst getMutationById = (id)=>{\n    return allMutations.find((mutation)=>mutation.id === id);\n};\n// Get mutations by type\nconst getMutationsByType = (type)=>{\n    switch(type){\n        case \"Growth\":\n            return growthMutations;\n        case \"Temperature\":\n            return temperatureMutations;\n        case \"Environmental\":\n            return environmentalMutations;\n        default:\n            return allMutations;\n    }\n};\n// Check mutation compatibility\nconst checkMutationCompatibility = (selectedMutations)=>{\n    for (const constraint of mutationConstraints){\n        const selectedInGroup = selectedMutations.filter((m)=>constraint.mutationIds.includes(m.id));\n        if (selectedInGroup.length > constraint.maxAllowed) {\n            return false;\n        }\n    }\n    return true;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/mutations.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/calculator.ts":
/*!*******************************!*\
  !*** ./src/lib/calculator.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateCropValue: () => (/* binding */ calculateCropValue),\n/* harmony export */   calculateMaxMultiplier: () => (/* binding */ calculateMaxMultiplier),\n/* harmony export */   calculateWeightImpact: () => (/* binding */ calculateWeightImpact),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatSheckles: () => (/* binding */ formatSheckles),\n/* harmony export */   getRecommendedWeight: () => (/* binding */ getRecommendedWeight),\n/* harmony export */   validateCalculatorInput: () => (/* binding */ validateCalculatorInput)\n/* harmony export */ });\n/* harmony import */ var _data_mutations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/data/mutations */ \"(ssr)/./src/data/mutations.ts\");\n\n/**\n * Core function to calculate crop value\n * Implemented based on the reference website's calculation formula\n */ function calculateCropValue(input) {\n    const { crop, weight, quantity, friendBoost, selectedMutations } = input;\n    if (!crop) {\n        return {\n            totalValue: 0,\n            totalMultiplier: 0,\n            baseValue: 0,\n            mutationMultiplier: 0,\n            details: {\n                cropName: \"\",\n                weight: 0,\n                quantity: 0,\n                friendBoost: 0,\n                mutations: []\n            }\n        };\n    }\n    // Check mutation compatibility\n    if (!(0,_data_mutations__WEBPACK_IMPORTED_MODULE_0__.checkMutationCompatibility)(selectedMutations)) {\n        throw new Error(\"Selected mutation combination is not compatible\");\n    }\n    // Calculate base value\n    let baseValue;\n    if (weight <= crop.minimumWeight) {\n        // When weight is less than or equal to minimum weight, use minimum value\n        baseValue = crop.minimumValue;\n    } else {\n        // When weight exceeds minimum weight, use complex formula: k × Weight²\n        const k = crop.k || 1;\n        baseValue = k * Math.pow(weight, 2);\n    }\n    // Calculate mutation multiplier\n    const mutationMultiplier = calculateMutationMultiplier(selectedMutations);\n    // Calculate friend boost (convert percentage to multiplier)\n    const friendMultiplier = 1 + friendBoost / 100;\n    // Calculate total multiplier\n    const totalMultiplier = mutationMultiplier * friendMultiplier;\n    // Calculate single crop value\n    const singleCropValue = baseValue * totalMultiplier;\n    // Calculate total value (considering quantity)\n    const totalValue = singleCropValue * quantity;\n    return {\n        totalValue: Math.round(totalValue),\n        totalMultiplier: Number(totalMultiplier.toFixed(2)),\n        baseValue: Math.round(baseValue),\n        mutationMultiplier: Number(mutationMultiplier.toFixed(2)),\n        details: {\n            cropName: crop.name,\n            weight: weight,\n            quantity: quantity,\n            friendBoost: friendBoost,\n            mutations: selectedMutations.map((m)=>m.name)\n        }\n    };\n}\n/**\n * Calculate mutation multiplier\n * Formula: Growth Mutation × (1 + Sum of Environmental Stack Bonuses)\n */ function calculateMutationMultiplier(mutations) {\n    if (mutations.length === 0) {\n        return 1;\n    }\n    // Separate growth and environmental mutations\n    const growthMutations = mutations.filter((m)=>m.type === \"Growth\");\n    const environmentalMutations = mutations.filter((m)=>m.type !== \"Growth\");\n    // Growth mutation multiplier (only one allowed)\n    const growthMultiplier = growthMutations.length > 0 ? growthMutations[0].multiplier : 1;\n    // Sum of environmental mutation stack bonuses\n    const environmentalStackBonus = environmentalMutations.reduce((sum, mutation)=>{\n        return sum + mutation.stackBonus;\n    }, 0);\n    // Total multiplier = Growth mutation multiplier × (1 + Sum of environmental stack bonuses)\n    return growthMultiplier * (1 + environmentalStackBonus);\n}\n/**\n * Format number display (add thousand separators)\n */ function formatNumber(num) {\n    if (num >= 1e12) {\n        return (num / 1e12).toFixed(1) + \"T\";\n    } else if (num >= 1e9) {\n        return (num / 1e9).toFixed(1) + \"B\";\n    } else if (num >= 1e6) {\n        return (num / 1e6).toFixed(1) + \"M\";\n    } else if (num >= 1e3) {\n        return (num / 1e3).toFixed(1) + \"K\";\n    } else {\n        return num.toLocaleString();\n    }\n}\n/**\n * Format Sheckles currency display\n */ function formatSheckles(amount) {\n    return `¢${formatNumber(amount)}`;\n}\n/**\n * Calculate maximum possible multiplier for mutation combination\n */ function calculateMaxMultiplier(mutations) {\n    if (mutations.length === 0) return 1;\n    // Find the highest growth mutation\n    const maxGrowthMultiplier = Math.max(...mutations.filter((m)=>m.type === \"Growth\").map((m)=>m.multiplier), 1);\n    // Calculate all environmental mutation stack bonuses\n    const totalEnvironmentalBonus = mutations.filter((m)=>m.type !== \"Growth\").reduce((sum, m)=>sum + m.stackBonus, 0);\n    return maxGrowthMultiplier * (1 + totalEnvironmentalBonus);\n}\n/**\n * Validate input data\n */ function validateCalculatorInput(input) {\n    const errors = [];\n    if (!input.crop) {\n        errors.push(\"Please select a crop\");\n    }\n    if (input.weight <= 0) {\n        errors.push(\"Weight must be greater than 0\");\n    }\n    if (input.quantity <= 0) {\n        errors.push(\"Quantity must be greater than 0\");\n    }\n    if (input.friendBoost < 0 || input.friendBoost > 1000) {\n        errors.push(\"Friend boost must be between 0-1000%\");\n    }\n    if (!(0,_data_mutations__WEBPACK_IMPORTED_MODULE_0__.checkMutationCompatibility)(input.selectedMutations)) {\n        errors.push(\"Selected mutation combination is not compatible\");\n    }\n    return errors;\n}\n/**\n * Get recommended crop weight (based on minimum weight)\n */ function getRecommendedWeight(crop) {\n    return crop.minimumWeight * 1.5; // Recommended weight is 1.5 times the minimum weight\n}\n/**\n * Calculate weight impact on value\n */ function calculateWeightImpact(crop, weight) {\n    if (weight < crop.minimumWeight) {\n        return {\n            isOptimal: false,\n            suggestion: `Weight below minimum weight ${crop.minimumWeight}kg, increase weight for higher value`\n        };\n    } else if (weight === crop.minimumWeight) {\n        return {\n            isOptimal: false,\n            suggestion: `Currently at minimum weight, increasing weight can significantly improve value`\n        };\n    } else {\n        return {\n            isOptimal: true,\n            suggestion: `Weight exceeds minimum weight, value grows quadratically`\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/calculator.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/lib/tw-merge.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2dyb3ctYS1nYXJkZW4tY2FsY3VsYXRvci8uL3NyYy9saWIvdXRpbHMudHM/N2MxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ3Jvdy1hLWdhcmRlbi1jYWxjdWxhdG9yLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9lYzhkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjk0MGM3N2M5MDU3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(rsc)/./src/components/layout/main-layout.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Grow A Garden Calculator - #1 Crop Value Calculator for Roblox\",\n    description: \"The ultimate Grow A Garden Calculator for Roblox. Calculate crop values with mutations, weights, and friend boosts. Complete database of 100+ crops and 40+ mutations. Free online tool.\",\n    keywords: \"Grow A Garden Calculator, Roblox Grow A Garden, Crop Calculator, Mutation Calculator, Crop Values, Roblox Calculator, Garden Calculator, Farming Calculator\",\n    authors: [\n        {\n            name: \"Grow A Garden Calculator Team\"\n        }\n    ],\n    creator: \"Grow A Garden Calculator\",\n    publisher: \"Grow A Garden Calculator\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"Grow A Garden Calculator - #1 Crop Value Calculator for Roblox\",\n        description: \"The ultimate Grow A Garden Calculator for Roblox. Calculate crop values with mutations, weights, and friend boosts. Complete database of 100+ crops and 40+ mutations.\",\n        url: \"https://growagardencalculator.com\",\n        siteName: \"Grow A Garden Calculator\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Grow A Garden Calculator - #1 Crop Value Calculator for Roblox\",\n        description: \"The ultimate Grow A Garden Calculator for Roblox. Calculate crop values with mutations, weights, and friend boosts.\",\n        creator: \"@GrowAGardenCalc\"\n    },\n    alternates: {\n        canonical: \"https://growagardencalculator.com\"\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\growagardencalculator\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\growagardencalculator\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\growagardencalculator\src\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/layout/main-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/main-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MainLayout: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\growagardencalculator\src\components\layout\main-layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\growagardencalculator\src\components\layout\main-layout.tsx#MainLayout`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5Cgrowagardencalculator%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5Cgrowagardencalculator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();