'use client';

import React from 'react';
import { MutationType } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

interface MutationFiltersProps {
  searchQuery: string;
  selectedType: string;
  minMultiplier: number;
  maxMultiplier: number;
  onSearchChange: (query: string) => void;
  onTypeChange: (type: string) => void;
  onMinMultiplierChange: (min: number) => void;
  onMaxMultiplierChange: (max: number) => void;
  onReset: () => void;
}

export function MutationFilters({
  searchQuery,
  selectedType,
  minMultiplier,
  maxMultiplier,
  onSearchChange,
  onTypeChange,
  onMinMultiplierChange,
  onMaxMultiplierChange,
  onReset
}: MutationFiltersProps) {
  const types: MutationType[] = ['Growth', 'Temperature', 'Environmental'];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle>Filter Mutations</CardTitle>
        <Button variant="outline" size="sm" onClick={onReset}>
          Reset Filters
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search Box */}
        <div>
          <Input
            placeholder="Search mutation names..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>

        {/* Type Filter */}
        <div>
          <label className="text-sm font-medium mb-2 block">Mutation Type</label>
          <Select value={selectedType} onValueChange={onTypeChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select mutation type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="Growth">⭐ Growth Mutations</SelectItem>
              <SelectItem value="Temperature">❄️ Temperature Mutations</SelectItem>
              <SelectItem value="Environmental">✨ Environmental Mutations</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Multiplier Range Filter */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Multiplier Range</label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Input
                type="number"
                placeholder="Min multiplier"
                value={minMultiplier || ''}
                onChange={(e) => onMinMultiplierChange(Number(e.target.value) || 0)}
                min="1"
              />
            </div>
            <div>
              <Input
                type="number"
                placeholder="Max multiplier"
                value={maxMultiplier || ''}
                onChange={(e) => onMaxMultiplierChange(Number(e.target.value) || 999)}
                min="1"
              />
            </div>
          </div>
        </div>

        {/* Quick Filter Buttons */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Quick Filters</label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onMinMultiplierChange(1);
                onMaxMultiplierChange(5);
              }}
            >
              Low Tier (1-5x)
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onMinMultiplierChange(6);
                onMaxMultiplierChange(20);
              }}
            >
              Mid Tier (6-20x)
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onMinMultiplierChange(21);
                onMaxMultiplierChange(99);
              }}
            >
              High Tier (21-99x)
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onMinMultiplierChange(100);
                onMaxMultiplierChange(999);
              }}
            >
              Ultra Tier (100x+)
            </Button>
          </div>
        </div>

        {/* Type Quick Select */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Filter by Type</label>
          <div className="space-y-1">
            <Button
              variant={selectedType === 'Growth' ? 'default' : 'outline'}
              size="sm"
              className="w-full justify-start"
              onClick={() => onTypeChange('Growth')}
            >
              ⭐ Growth Mutations
            </Button>
            <Button
              variant={selectedType === 'Temperature' ? 'default' : 'outline'}
              size="sm"
              className="w-full justify-start"
              onClick={() => onTypeChange('Temperature')}
            >
              ❄️ Temperature Mutations
            </Button>
            <Button
              variant={selectedType === 'Environmental' ? 'default' : 'outline'}
              size="sm"
              className="w-full justify-start"
              onClick={() => onTypeChange('Environmental')}
            >
              ✨ Environmental Mutations
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
