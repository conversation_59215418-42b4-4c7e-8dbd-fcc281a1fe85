'use client';

import React from 'react';
import { Crop } from '@/types';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { getRecommendedWeight, calculateWeightImpact } from '@/lib/calculator';

interface CalculationInputsProps {
  crop: Crop | null;
  weight: number;
  quantity: number;
  friendBoost: number;
  onWeightChange: (weight: number) => void;
  onQuantityChange: (quantity: number) => void;
  onFriendBoostChange: (boost: number) => void;
  onReset: () => void;
}

export function CalculationInputs({
  crop,
  weight,
  quantity,
  friendBoost,
  onWeightChange,
  onQuantityChange,
  onFriendBoostChange,
  onReset
}: CalculationInputsProps) {
  const weightImpact = crop ? calculateWeightImpact(crop, weight) : null;
  const recommendedWeight = crop ? getRecommendedWeight(crop) : 0;

  const handleWeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    onWeightChange(Math.max(0, value));
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    onQuantityChange(Math.max(0, value));
  };

  const handleFriendBoostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    onFriendBoostChange(Math.max(0, Math.min(1000, value)));
  };

  const setRecommendedWeight = () => {
    if (crop) {
      onWeightChange(recommendedWeight);
    }
  };

  const setMinimumWeight = () => {
    if (crop) {
      onWeightChange(crop.minimumWeight);
    }
  };

  return (
    <Card className="bg-card/50 border-border/50">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Calculation Parameters</CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Weight Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Weight (kg)</label>
            <div className="flex gap-2">
              <Input
                type="number"
                value={weight}
                onChange={handleWeightChange}
                placeholder="2.85"
                step="0.01"
                min="0"
                className="bg-background/50 border-border/50 text-sm"
              />
              {crop && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={setRecommendedWeight}
                  className="h-10 w-10 flex-shrink-0"
                  title="Set to recommended weight"
                  type="button"
                >
                  ✓
                </Button>
              )}
            </div>
            {crop && (
              <div className="text-xs text-muted-foreground">
                Min: {crop.minimumWeight}kg | Recommended: {recommendedWeight}kg
              </div>
            )}
          </div>

          {/* Quantity Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Quantity (pcs)</label>
            <Input
              type="number"
              value={quantity}
              onChange={handleQuantityChange}
              placeholder="1"
              min="1"
              className="bg-background/50 border-border/50 text-sm h-10"
            />
            <div className="text-xs text-muted-foreground">
              Number of crops to harvest
            </div>
          </div>

          {/* Friend Boost Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Friend Boost</label>
            <div className="flex items-center gap-2">
              <input
                type="range"
                min="0"
                max="1000"
                step="1"
                value={friendBoost}
                onChange={handleFriendBoostChange}
                className="flex-1 h-2 bg-background/50 rounded-lg appearance-none cursor-pointer"
              />
              <span className="text-sm font-medium w-12">{friendBoost}%</span>
            </div>
            <div className="text-xs text-muted-foreground">
              Boost from friends (0-1000%)
            </div>
          </div>

          {/* Reset Button */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Reset Calculator</label>
            <Button
              variant="outline"
              size="sm"
              onClick={onReset}
              className="w-full bg-background/50 border-border/50 text-sm h-10"
              type="button"
            >
              Reset All Inputs
            </Button>
            <div className="text-xs text-muted-foreground">
              Clear all selections
            </div>
          </div>
        </div>

        {/* Current Crop Information */}
        {crop && (
          <div className="mt-4 pt-4 border-t space-y-2">
            <h4 className="text-sm font-semibold">Crop Information</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs bg-background/50 rounded-lg border border-border/30 p-3">
              <div>
                <span className="text-muted-foreground">Tier:</span>
                <span className="ml-1 font-medium">{crop.tier}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Harvest Type:</span>
                <span className="ml-1 font-medium">{crop.harvestType}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Min Value:</span>
                <span className="ml-1 font-medium">¢{crop.minimumValue.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Seed Price:</span>
                <span className="ml-1 font-medium">¢{crop.shecklePrice.toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
