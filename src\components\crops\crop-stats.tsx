'use client';

import React from 'react';
import { Crop } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatSheckles } from '@/lib/calculator';

interface CropStatsProps {
  crops: Crop[];
  filteredCrops: Crop[];
}

export function CropStats({ crops, filteredCrops }: CropStatsProps) {
  // Calculate statistics
  const stats = React.useMemo(() => {
    const totalCrops = crops.length;
    const filteredCount = filteredCrops.length;

    // Count by tier
    const tierCounts = filteredCrops.reduce((acc, crop) => {
      acc[crop.tier] = (acc[crop.tier] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Count by harvest type
    const harvestTypeCounts = filteredCrops.reduce((acc, crop) => {
      acc[crop.harvestType] = (acc[crop.harvestType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Price statistics
    const prices = filteredCrops.map(crop => crop.shecklePrice);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;

    // Value statistics
    const values = filteredCrops.map(crop => crop.minimumValue);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const avgValue = values.reduce((sum, value) => sum + value, 0) / values.length;

    return {
      totalCrops,
      filteredCount,
      tierCounts,
      harvestTypeCounts,
      priceStats: { min: minPrice, max: maxPrice, avg: avgPrice },
      valueStats: { min: minValue, max: maxValue, avg: avgValue }
    };
  }, [crops, filteredCrops]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Crop Statistics</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Statistics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{stats.filteredCount}</div>
            <div className="text-sm text-muted-foreground">Showing</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-muted-foreground">{stats.totalCrops}</div>
            <div className="text-sm text-muted-foreground">Total Crops</div>
          </div>
        </div>

        {/* Tier Distribution */}
        <div>
          <h4 className="font-semibold mb-2">Tier Distribution</h4>
          <div className="space-y-1">
            {Object.entries(stats.tierCounts).map(([tier, count]) => (
              <div key={tier} className="flex justify-between text-sm">
                <span>{tier}</span>
                <span className="font-medium">{count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Harvest Type Distribution */}
        <div>
          <h4 className="font-semibold mb-2">Harvest Types</h4>
          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span>Single Harvest</span>
              <span className="font-medium">{stats.harvestTypeCounts.Single || 0}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Multi Harvest</span>
              <span className="font-medium">{stats.harvestTypeCounts.Multi || 0}</span>
            </div>
          </div>
        </div>

        {/* Price Statistics */}
        {stats.filteredCount > 0 && (
          <div>
            <h4 className="font-semibold mb-2">Seed Price Stats</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Minimum Price</span>
                <span className="font-medium">{formatSheckles(stats.priceStats.min)}</span>
              </div>
              <div className="flex justify-between">
                <span>Maximum Price</span>
                <span className="font-medium">{formatSheckles(stats.priceStats.max)}</span>
              </div>
              <div className="flex justify-between">
                <span>Average Price</span>
                <span className="font-medium">{formatSheckles(Math.round(stats.priceStats.avg))}</span>
              </div>
            </div>
          </div>
        )}

        {/* Value Statistics */}
        {stats.filteredCount > 0 && (
          <div>
            <h4 className="font-semibold mb-2">Minimum Value Stats</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Minimum Value</span>
                <span className="font-medium">{formatSheckles(stats.valueStats.min)}</span>
              </div>
              <div className="flex justify-between">
                <span>Maximum Value</span>
                <span className="font-medium">{formatSheckles(stats.valueStats.max)}</span>
              </div>
              <div className="flex justify-between">
                <span>Average Value</span>
                <span className="font-medium">{formatSheckles(Math.round(stats.valueStats.avg))}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
