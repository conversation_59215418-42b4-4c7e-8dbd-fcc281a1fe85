'use client';

import React from 'react';
import Image from 'next/image';
import { CalculationResult as CalculationResultType } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatSheckles, formatNumber } from '@/lib/calculator';

interface CalculationResultProps {
  result: CalculationResultType;
}

export function CalculationResult({ result }: CalculationResultProps) {
  if (!result.details.cropName) {
    return (
      <Card className="bg-card/50 border-border/50">
        <CardContent className="p-4">
          <div className="text-center py-8 text-muted-foreground">
            Please select a crop and enter parameters to start calculating
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-card/50 border-border/50">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          💰 Calculation Result
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        {/* Total Value Display */}
        <div className="text-center space-y-2 mb-6 bg-primary/5 border border-primary/20 rounded-lg p-6">
          <div className="text-sm text-muted-foreground">Total Value</div>
          <div className="text-4xl font-bold text-primary">
            {formatSheckles(result.totalValue)}
          </div>
          <div className="text-sm text-muted-foreground">
            {result.details.quantity > 1 && `${result.details.quantity} crops × ${formatSheckles(Math.round(result.totalValue / result.details.quantity))} each`}
          </div>
        </div>

        {/* Calculation Breakdown */}
        <div className="space-y-4">
          <div className="bg-background/50 rounded-lg border border-border/30 p-4">
            <h4 className="font-medium text-sm mb-3">Calculation Breakdown</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Base Value:</span>
                <span className="font-medium">{formatSheckles(result.baseValue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Mutation Multiplier:</span>
                <span className="font-medium">×{formatNumber(result.mutationMultiplier)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Friend Boost:</span>
                <span className="font-medium">+{result.details.friendBoost}%</span>
              </div>
              <div className="border-t pt-2 flex justify-between font-semibold">
                <span>Total Multiplier:</span>
                <span className="text-primary">×{formatNumber(result.totalMultiplier)}</span>
              </div>
            </div>
          </div>

          {/* Crop Details */}
          <div className="bg-background/50 rounded-lg border border-border/30 p-4">
            <h4 className="font-medium text-sm mb-3">Crop Details</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Crop:</span>
                <span className="font-medium">{result.details.cropName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Weight:</span>
                <span className="font-medium">{result.details.weight}kg</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Quantity:</span>
                <span className="font-medium">{result.details.quantity} pcs</span>
              </div>
            </div>
          </div>

          {/* Active Mutations */}
          {result.details.mutations.length > 0 && (
            <div className="bg-background/50 rounded-lg border border-border/30 p-4">
              <h4 className="font-medium text-sm mb-3">Active Mutations</h4>
              <div className="flex flex-wrap gap-2">
                {result.details.mutations.map((mutationName, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-primary/10 border border-primary/30 rounded-full text-xs font-medium"
                  >
                    {mutationName}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
