'use client';

import React from 'react';
import Image from 'next/image';
import { CalculationResult as CalculationResultType } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatSheckles, formatNumber } from '@/lib/calculator';

interface CalculationResultProps {
  result: CalculationResultType;
}

export function CalculationResult({ result }: CalculationResultProps) {
  if (!result.details.cropName) {
    return (
      <Card className="bg-card/50 border-border/50">
        <CardContent className="p-4">
          <div className="text-center py-8 text-muted-foreground">
            Please select a crop and enter parameters to start calculating
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-card/50 border-border/50">
      <CardContent className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <span className="text-sm font-medium">💰 Total Value</span>
        </div>

        {/* Total Value Display */}
        <div className="text-center space-y-2 mb-6">
          <div className="text-4xl font-bold text-primary">
            {formatSheckles(result.totalValue)}
          </div>
        </div>

        {/* Calculation Details */}
        <div className="space-y-3">
          <div className="text-sm">
            <span className="text-muted-foreground">Total Multiplier: </span>
            <span className="font-medium">×{formatNumber(result.totalMultiplier)}</span>
          </div>

          <div className="text-sm">
            <span className="text-muted-foreground">Valuation Details</span>
          </div>

          <div className="flex items-center gap-2 text-sm">
            <div className="w-3 h-3 bg-primary rounded"></div>
            <span className="font-medium">{result.details.cropName}</span>
            <span className="text-muted-foreground">
              weighing {result.details.weight}kg, worth {formatSheckles(result.baseValue)}
            </span>
          </div>
        </div>

        {/* This section is no longer needed as we've moved it to the main display */}

        {/* This section is simplified in the new design */}
      </CardContent>
    </Card>
  );
}
