'use client';

import React from 'react';
import { Crop, Mutation, CalculatorInput, CalculationResult } from '@/types';
import { calculateCropValue, validateCalculatorInput } from '@/lib/calculator';
import { CropSelector } from '@/components/calculator/crop-selector';
import { MutationSelector } from '@/components/calculator/mutation-selector';
import { CalculationInputs } from '@/components/calculator/calculation-inputs';
import { CalculationResult as CalculationResultComponent } from '@/components/calculator/calculation-result';

export default function HomePage() {
  const [selectedCrop, setSelectedCrop] = React.useState<Crop | null>(null);
  const [selectedMutations, setSelectedMutations] = React.useState<Mutation[]>([]);
  const [weight, setWeight] = React.useState<number>(1);
  const [quantity, setQuantity] = React.useState<number>(1);
  const [friendBoost, setFriendBoost] = React.useState<number>(0);
  const [result, setResult] = React.useState<CalculationResult>({
    totalValue: 0,
    totalMultiplier: 0,
    baseValue: 0,
    mutationMultiplier: 0,
    details: {
      cropName: '',
      weight: 0,
      quantity: 0,
      friendBoost: 0,
      mutations: []
    }
  });

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Grow A Garden Calculator",
    "description": "The ultimate crop value calculator for Roblox Grow A Garden with mutations, weights, and friend boosts",
    "url": "https://growagardencalculator.com",
    "applicationCategory": "GameApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "featureList": [
      "Crop value calculation",
      "Mutation system",
      "100+ crops database",
      "40+ mutations database",
      "Friend boost calculation"
    ]
  };

  // Recalculate when inputs change
  React.useEffect(() => {
    if (selectedCrop) {
      const input: CalculatorInput = {
        crop: selectedCrop,
        weight,
        quantity,
        friendBoost,
        selectedMutations
      };

      try {
        const errors = validateCalculatorInput(input);
        if (errors.length === 0) {
          const newResult = calculateCropValue(input);
          setResult(newResult);
        }
      } catch (error) {
        console.error('Calculation error:', error);
      }
    }
  }, [selectedCrop, weight, quantity, friendBoost, selectedMutations]);

  // When selecting a new crop, set recommended weight
  const handleCropSelect = (crop: Crop) => {
    setSelectedCrop(crop);
    setWeight(crop.minimumWeight * 1.5); // Set to recommended weight
  };

  // Reset all inputs
  const handleReset = () => {
    setSelectedCrop(null);
    setSelectedMutations([]);
    setWeight(1);
    setQuantity(1);
    setFriendBoost(0);
    setResult({
      totalValue: 0,
      totalMultiplier: 0,
      baseValue: 0,
      mutationMultiplier: 0,
      details: {
        cropName: '',
        weight: 0,
        quantity: 0,
        friendBoost: 0,
        mutations: []
      }
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Structured data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      {/* Page Title */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-2">Grow A Garden Calculator</h1>
        <p className="text-lg text-muted-foreground mb-4">
          The ultimate crop value calculator for Roblox Grow A Garden
        </p>
        <div className="max-w-3xl mx-auto text-sm text-muted-foreground">
          <p>
            Calculate precise crop values with mutations, weights, and friend boosts.
            Features complete database of 100+ crops and 40+ mutations.
            Free online tool for Roblox Grow A Garden players.
          </p>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="space-y-6 max-w-4xl mx-auto bg-background/80">
        {/* Crop Selector Section */}
        <div className="w-full">
          <CropSelector
            selectedCrop={selectedCrop}
            onCropSelect={handleCropSelect}
          />
        </div>

        {/* Mutation Selector Section */}
        <div className="w-full">
          <MutationSelector
            selectedMutations={selectedMutations}
            onMutationsChange={setSelectedMutations}
          />
        </div>

        {/* Calculation Inputs Section */}
        <div className="w-full">
          <CalculationInputs
            crop={selectedCrop}
            weight={weight}
            quantity={quantity}
            friendBoost={friendBoost}
            onWeightChange={setWeight}
            onQuantityChange={setQuantity}
            onFriendBoostChange={setFriendBoost}
            onReset={handleReset}
          />
        </div>

        {/* Results Section */}
        <div className="w-full">
          <CalculationResultComponent result={result} />
        </div>
      </div>


    </div>
  );
}
