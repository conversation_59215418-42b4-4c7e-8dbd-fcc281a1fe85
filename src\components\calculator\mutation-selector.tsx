'use client';

import React from 'react';
import Image from 'next/image';
import { Mutation } from '@/types';
import { growthMutations, temperatureMutations, environmentalMutations, checkMutationCompatibility } from '@/data/mutations';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface MutationSelectorProps {
  selectedMutations: Mutation[];
  onMutationsChange: (mutations: Mutation[]) => void;
}

export function MutationSelector({ selectedMutations, onMutationsChange }: MutationSelectorProps) {
  const toggleMutation = (mutation: Mutation) => {
    const isSelected = selectedMutations.some(m => m.id === mutation.id);
    
    let newMutations: Mutation[];
    if (isSelected) {
      // Remove mutation
      newMutations = selectedMutations.filter(m => m.id !== mutation.id);
    } else {
      // Add mutation
      newMutations = [...selectedMutations, mutation];
    }

    // Check compatibility
    if (checkMutationCompatibility(newMutations)) {
      onMutationsChange(newMutations);
    }
  };

  const clearAllMutations = () => {
    onMutationsChange([]);
  };

  const renderMutationGroup = (title: string, mutations: Mutation[]) => (
    <div className="space-y-3">
      <h4 className="font-semibold text-sm flex items-center gap-2">
        {title.includes('Growth') && <span className="text-yellow-500 text-base">⭐</span>}
        {title.includes('Temperature') && <span className="text-blue-500 text-base">❄️</span>}
        {title.includes('Environmental') && <span className="text-green-500 text-base">✨</span>}
        {title.replace(/[⭐❄️✨]\s*/, '')}
      </h4>
      <div className="bg-background/30 p-4 rounded-lg border border-border/30">
        <div className="flex flex-wrap gap-2">
          {mutations.map((mutation) => {
            const isSelected = selectedMutations.some(m => m.id === mutation.id);
            const testMutations = isSelected
              ? selectedMutations
              : [...selectedMutations, mutation];
            const isCompatible = checkMutationCompatibility(testMutations);

            return (
              <button
                key={mutation.id}
                onClick={() => toggleMutation(mutation)}
                disabled={!isSelected && !isCompatible}
                className={`
                  px-4 py-1.5 rounded-full text-sm transition-all
                  ${isSelected
                    ? 'bg-primary/20 border-2 border-primary text-primary shadow-sm'
                    : isCompatible
                      ? 'bg-background/50 border border-border/50 hover:border-primary/50 hover:bg-background/80'
                      : 'bg-background/30 border border-border/30 opacity-50 cursor-not-allowed'
                  }
                `}
                title={mutation.description}
                type="button"
              >
                <span className="font-medium">{mutation.name}</span>
                <span className="ml-1.5 text-xs font-semibold opacity-80">(×{mutation.multiplier})</span>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );

  return (
    <Card className="bg-card/50 border-border/50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Select Mutations</CardTitle>
          {selectedMutations.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllMutations}
              className="text-xs"
            >
              Clear All ({selectedMutations.length})
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-0 space-y-6">
        {/* Growth Mutations */}
        {renderMutationGroup('⭐ Growth Mutations', growthMutations)}

        {/* Temperature Mutations */}
        {renderMutationGroup('❄️ Temperature Mutations', temperatureMutations)}

        {/* Environmental Mutations */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold text-sm flex items-center gap-2">
              <span className="text-green-500 text-base">✨</span>
              Environmental Mutations
            </h4>
            <div className="relative">
              <Input
                placeholder="Search mutations..."
                className="w-48 h-8 text-sm bg-background/50 border-border/50"
              />
            </div>
          </div>
          <div className="bg-background/30 p-4 rounded-lg border border-border/30">
            <div className="flex flex-wrap gap-2">
              {environmentalMutations.map((mutation) => {
                const isSelected = selectedMutations.some(m => m.id === mutation.id);
                const testMutations = isSelected
                  ? selectedMutations
                  : [...selectedMutations, mutation];
                const isCompatible = checkMutationCompatibility(testMutations);

                return (
                  <button
                    key={mutation.id}
                    onClick={() => toggleMutation(mutation)}
                    disabled={!isSelected && !isCompatible}
                    className={`
                      px-4 py-1.5 rounded-full text-sm transition-all
                      ${isSelected
                        ? 'bg-primary/20 border-2 border-primary text-primary shadow-sm'
                        : isCompatible
                          ? 'bg-background/50 border border-border/50 hover:border-primary/50 hover:bg-background/80'
                          : 'bg-background/30 border border-border/30 opacity-50 cursor-not-allowed'
                      }
                    `}
                    title={mutation.description}
                    type="button"
                  >
                    <span className="font-medium">{mutation.name}</span>
                    <span className="ml-1.5 text-xs font-semibold opacity-80">(×{mutation.multiplier})</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Selected Mutations Summary */}
        {selectedMutations.length > 0 && (
          <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
            <h5 className="font-medium text-sm mb-2">Selected Mutations ({selectedMutations.length})</h5>
            <div className="flex flex-wrap gap-2">
              {selectedMutations.map((mutation) => (
                <span
                  key={mutation.id}
                  className="px-3 py-1 bg-primary/10 border border-primary/30 rounded-full text-xs font-medium"
                >
                  {mutation.name} (×{mutation.multiplier})
                </span>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
