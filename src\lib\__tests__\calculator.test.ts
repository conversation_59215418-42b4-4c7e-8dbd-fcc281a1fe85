import { calculateCropValue, formatSheckles, formatNumber } from '../calculator';
import { getCropById } from '../../data/crops';
import { getMutationById } from '../../data/mutations';

describe('Calculator Functions', () => {
  describe('calculateCropValue', () => {
    test('should calculate basic crop value without mutations', () => {
      const carrot = getCropById('carrot');
      if (!carrot) throw new Error('Carrot not found');

      const result = calculateCropValue({
        crop: carrot,
        weight: 1,
        quantity: 1,
        friendBoost: 0,
        selectedMutations: []
      });

      expect(result.baseValue).toBe(312.5); // k × weight² = 312.5 × 1²
      expect(result.mutationMultiplier).toBe(1);
      expect(result.totalMultiplier).toBe(1);
      expect(result.totalValue).toBe(312.5);
    });

    test('should calculate crop value with minimum weight', () => {
      const carrot = getCropById('carrot');
      if (!carrot) throw new Error('Carrot not found');

      const result = calculateCropValue({
        crop: carrot,
        weight: 0.1, // Below minimum weight
        quantity: 1,
        friendBoost: 0,
        selectedMutations: []
      });

      expect(result.baseValue).toBe(carrot.minimumValue); // Should use minimum value
      expect(result.totalValue).toBe(carrot.minimumValue);
    });

    test('should calculate crop value with golden mutation', () => {
      const carrot = getCropById('carrot');
      const golden = getMutationById('golden');
      if (!carrot || !golden) throw new Error('Crop or mutation not found');

      const result = calculateCropValue({
        crop: carrot,
        weight: 1,
        quantity: 1,
        friendBoost: 0,
        selectedMutations: [golden]
      });

      expect(result.mutationMultiplier).toBe(20); // Golden mutation multiplier
      expect(result.totalValue).toBe(312.5 * 20); // Base value × mutation multiplier
    });

    test('should calculate crop value with friend boost', () => {
      const carrot = getCropById('carrot');
      if (!carrot) throw new Error('Carrot not found');

      const result = calculateCropValue({
        crop: carrot,
        weight: 1,
        quantity: 1,
        friendBoost: 50, // 50% friend boost
        selectedMutations: []
      });

      expect(result.totalMultiplier).toBe(1.5); // 1 + 50%
      expect(result.totalValue).toBe(312.5 * 1.5);
    });

    test('should calculate crop value with multiple quantities', () => {
      const carrot = getCropById('carrot');
      if (!carrot) throw new Error('Carrot not found');

      const result = calculateCropValue({
        crop: carrot,
        weight: 1,
        quantity: 5,
        friendBoost: 0,
        selectedMutations: []
      });

      expect(result.totalValue).toBe(312.5 * 5);
    });

    test('should calculate crop value with environmental mutations', () => {
      const carrot = getCropById('carrot');
      const wet = getMutationById('wet');
      const pollinated = getMutationById('pollinated');
      if (!carrot || !wet || !pollinated) throw new Error('Crop or mutations not found');

      const result = calculateCropValue({
        crop: carrot,
        weight: 1,
        quantity: 1,
        friendBoost: 0,
        selectedMutations: [wet, pollinated]
      });

      // Environmental mutations stack: 1 + (wet.stackBonus + pollinated.stackBonus)
      // wet.stackBonus = 1, pollinated.stackBonus = 2
      expect(result.mutationMultiplier).toBe(1 + 1 + 2); // 1 + 3 = 4
      expect(result.totalValue).toBe(312.5 * 4);
    });
  });

  describe('formatSheckles', () => {
    test('should format small numbers correctly', () => {
      expect(formatSheckles(100)).toBe('¢100');
      expect(formatSheckles(999)).toBe('¢999');
    });

    test('should format thousands correctly', () => {
      expect(formatSheckles(1000)).toBe('¢1.0K');
      expect(formatSheckles(1500)).toBe('¢1.5K');
      expect(formatSheckles(999999)).toBe('¢1000.0K');
    });

    test('should format millions correctly', () => {
      expect(formatSheckles(1000000)).toBe('¢1.0M');
      expect(formatSheckles(1500000)).toBe('¢1.5M');
    });

    test('should format billions correctly', () => {
      expect(formatSheckles(1000000000)).toBe('¢1.0B');
      expect(formatSheckles(1500000000)).toBe('¢1.5B');
    });

    test('should format trillions correctly', () => {
      expect(formatSheckles(1000000000000)).toBe('¢1.0T');
      expect(formatSheckles(1500000000000)).toBe('¢1.5T');
    });
  });

  describe('formatNumber', () => {
    test('should format numbers with appropriate suffixes', () => {
      expect(formatNumber(100)).toBe('100');
      expect(formatNumber(1000)).toBe('1.0K');
      expect(formatNumber(1000000)).toBe('1.0M');
      expect(formatNumber(1000000000)).toBe('1.0B');
      expect(formatNumber(1000000000000)).toBe('1.0T');
    });
  });
});

// Mock data for testing if needed
jest.mock('../../data/crops', () => ({
  getCropById: jest.fn(),
  crops: []
}));

jest.mock('../../data/mutations', () => ({
  getMutationById: jest.fn(),
  allMutations: []
}));
